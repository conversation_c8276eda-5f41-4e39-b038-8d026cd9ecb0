
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31813.321
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UniversalUI.CommandLineSourceGenerator", "src\UniversalUI.CommandLineSourceGenerator\UniversalUI.CommandLineSourceGenerator.csproj", "{740943F5-2E99-4ED4-9022-BC14F480C3F8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "samples", "samples", "{9B71283D-A4A6-480E-9A9B-AD446F08CCC5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UniversalUI.Analyzers", "src\UniversalUI.Analyzers\UniversalUI.Analyzers.csproj", "{26E564DA-4315-44EF-B287-A9B3BC87BF1C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "controls", "controls", "{9FA8F699-79F5-48DB-A2D9-717CE875C5F0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Microcharts", "samples\controls\Microcharts\Microcharts.csproj", "{BF454D87-C790-4E9E-8E32-772DF3CA65BF}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "host-apps", "host-apps", "{63D6491B-37EA-4F36-8B9D-2AD526F74BB8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "WpfSamplesHost", "samples\host-apps\WpfSamplesHost\WpfSamplesHost.csproj", "{8E651CB1-4524-4A5B-A47C-C1A51C73C775}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MauiSamplesHost", "samples\host-apps\MauiSamplesHost\MauiSamplesHost.csproj", "{5890241C-A3EF-4CEE-BA67-BA4EFD57E80C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SimpleControls", "samples\controls\SimpleControls\SimpleControls.csproj", "{CFF4EEBA-0EA7-49D1-B4F2-17DF640F2174}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{1D9AD821-E4C9-44C5-9807-DC296C17A38B}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		.gitignore = .gitignore
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "skia", "skia", "{705D949C-2C10-46AD-9007-37E62BC8FA7A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UniversalUI.SkiaVisualFramework", "src\skia\UniversalUI.SkiaVisualFramework\UniversalUI.SkiaVisualFramework.csproj", "{66AC78D8-0A73-41FC-925C-E2840EA2639F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "wpf", "wpf", "{53F78F6D-A2D0-41D5-8A81-7D71C5763249}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UniversalUI.FrameworkForwarder-wpf", "src\wpf\UniversalUI.FrameworkForwarder\UniversalUI.FrameworkForwarder-wpf.csproj", "{29322FD8-3BA2-47E3-A8D9-E9C918979670}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UniversalUI.CommonTypes", "src\UniversalUI.CommonTypes\UniversalUI.CommonTypes.csproj", "{045EF11F-5494-426E-B7B3-660F1FCF23D5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UniversalUI.Wpf", "src\wpf\UniversalUI.Wpf\UniversalUI.Wpf.csproj", "{E289961D-B4F4-4368-9402-A0057A009122}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UniversalUI.FrameworkForwarder-reference", "src\UniversalUI.FrameworkForwarderStub\UniversalUI.FrameworkForwarder-reference.csproj", "{1DF10122-622E-4A4C-93C7-6172997D8332}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "maui", "maui", "{3EF85AAC-0972-46C4-BABE-FF568E7A9BE7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UniversalUI.Maui", "src\maui\UniversalUI.Maui\UniversalUI.Maui.csproj", "{069C0BDF-E992-443B-A1C0-216F18216BFE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UniversalUI.FrameworkForwarder-maui", "src\maui\UniversalUI.FrameworkForwarder\UniversalUI.FrameworkForwarder-maui.csproj", "{73A019ED-4595-4E33-A95A-F502071E32D7}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "external", "external", "{634ED71E-3E71-4F43-B9A2-11536F91EAA8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "example-framework", "example-framework", "{2383D6A4-790C-4661-A5A4-70C051F3DDD4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ExampleFramework", "external\example-framework\src\ExampleFramework\ExampleFramework.csproj", "{11E03881-DB12-47D2-B517-BAA3D0CCE7F6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VisualTestUtils.AppConnector", "external\example-framework\src\visual-test-utils\VisualTestUtils.AppConnector\VisualTestUtils.AppConnector.csproj", "{56BE1E11-9CC5-4F03-807F-E77A5F7710C3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VisualTestUtils", "external\example-framework\src\visual-test-utils\VisualTestUtils\VisualTestUtils.csproj", "{94911763-A899-4C80-87C8-61CF09448DCB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{3A04AA0A-A8A4-4ACC-A171-32EE16ADDC5C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UniversalUI-skia", "src\UniversalUI\UniversalUI-skia.csproj", "{C27759DB-0C5D-BD2F-F7EC-01E759C4A8E2}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{740943F5-2E99-4ED4-9022-BC14F480C3F8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{740943F5-2E99-4ED4-9022-BC14F480C3F8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{740943F5-2E99-4ED4-9022-BC14F480C3F8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{740943F5-2E99-4ED4-9022-BC14F480C3F8}.Release|Any CPU.Build.0 = Release|Any CPU
		{26E564DA-4315-44EF-B287-A9B3BC87BF1C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{26E564DA-4315-44EF-B287-A9B3BC87BF1C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{26E564DA-4315-44EF-B287-A9B3BC87BF1C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{26E564DA-4315-44EF-B287-A9B3BC87BF1C}.Release|Any CPU.Build.0 = Release|Any CPU
		{BF454D87-C790-4E9E-8E32-772DF3CA65BF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BF454D87-C790-4E9E-8E32-772DF3CA65BF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BF454D87-C790-4E9E-8E32-772DF3CA65BF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BF454D87-C790-4E9E-8E32-772DF3CA65BF}.Release|Any CPU.Build.0 = Release|Any CPU
		{8E651CB1-4524-4A5B-A47C-C1A51C73C775}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8E651CB1-4524-4A5B-A47C-C1A51C73C775}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8E651CB1-4524-4A5B-A47C-C1A51C73C775}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8E651CB1-4524-4A5B-A47C-C1A51C73C775}.Release|Any CPU.Build.0 = Release|Any CPU
		{5890241C-A3EF-4CEE-BA67-BA4EFD57E80C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5890241C-A3EF-4CEE-BA67-BA4EFD57E80C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5890241C-A3EF-4CEE-BA67-BA4EFD57E80C}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{5890241C-A3EF-4CEE-BA67-BA4EFD57E80C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5890241C-A3EF-4CEE-BA67-BA4EFD57E80C}.Release|Any CPU.Build.0 = Release|Any CPU
		{5890241C-A3EF-4CEE-BA67-BA4EFD57E80C}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{CFF4EEBA-0EA7-49D1-B4F2-17DF640F2174}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CFF4EEBA-0EA7-49D1-B4F2-17DF640F2174}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CFF4EEBA-0EA7-49D1-B4F2-17DF640F2174}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CFF4EEBA-0EA7-49D1-B4F2-17DF640F2174}.Release|Any CPU.Build.0 = Release|Any CPU
		{66AC78D8-0A73-41FC-925C-E2840EA2639F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{66AC78D8-0A73-41FC-925C-E2840EA2639F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{66AC78D8-0A73-41FC-925C-E2840EA2639F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{66AC78D8-0A73-41FC-925C-E2840EA2639F}.Release|Any CPU.Build.0 = Release|Any CPU
		{29322FD8-3BA2-47E3-A8D9-E9C918979670}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{29322FD8-3BA2-47E3-A8D9-E9C918979670}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{29322FD8-3BA2-47E3-A8D9-E9C918979670}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{29322FD8-3BA2-47E3-A8D9-E9C918979670}.Release|Any CPU.Build.0 = Release|Any CPU
		{045EF11F-5494-426E-B7B3-660F1FCF23D5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{045EF11F-5494-426E-B7B3-660F1FCF23D5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{045EF11F-5494-426E-B7B3-660F1FCF23D5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{045EF11F-5494-426E-B7B3-660F1FCF23D5}.Release|Any CPU.Build.0 = Release|Any CPU
		{E289961D-B4F4-4368-9402-A0057A009122}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E289961D-B4F4-4368-9402-A0057A009122}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E289961D-B4F4-4368-9402-A0057A009122}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E289961D-B4F4-4368-9402-A0057A009122}.Release|Any CPU.Build.0 = Release|Any CPU
		{1DF10122-622E-4A4C-93C7-6172997D8332}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1DF10122-622E-4A4C-93C7-6172997D8332}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1DF10122-622E-4A4C-93C7-6172997D8332}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1DF10122-622E-4A4C-93C7-6172997D8332}.Release|Any CPU.Build.0 = Release|Any CPU
		{069C0BDF-E992-443B-A1C0-216F18216BFE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{069C0BDF-E992-443B-A1C0-216F18216BFE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{069C0BDF-E992-443B-A1C0-216F18216BFE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{069C0BDF-E992-443B-A1C0-216F18216BFE}.Release|Any CPU.Build.0 = Release|Any CPU
		{73A019ED-4595-4E33-A95A-F502071E32D7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{73A019ED-4595-4E33-A95A-F502071E32D7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{73A019ED-4595-4E33-A95A-F502071E32D7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{73A019ED-4595-4E33-A95A-F502071E32D7}.Release|Any CPU.Build.0 = Release|Any CPU
		{11E03881-DB12-47D2-B517-BAA3D0CCE7F6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{11E03881-DB12-47D2-B517-BAA3D0CCE7F6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{11E03881-DB12-47D2-B517-BAA3D0CCE7F6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{11E03881-DB12-47D2-B517-BAA3D0CCE7F6}.Release|Any CPU.Build.0 = Release|Any CPU
		{56BE1E11-9CC5-4F03-807F-E77A5F7710C3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{56BE1E11-9CC5-4F03-807F-E77A5F7710C3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{56BE1E11-9CC5-4F03-807F-E77A5F7710C3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{56BE1E11-9CC5-4F03-807F-E77A5F7710C3}.Release|Any CPU.Build.0 = Release|Any CPU
		{94911763-A899-4C80-87C8-61CF09448DCB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{94911763-A899-4C80-87C8-61CF09448DCB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{94911763-A899-4C80-87C8-61CF09448DCB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{94911763-A899-4C80-87C8-61CF09448DCB}.Release|Any CPU.Build.0 = Release|Any CPU
		{C27759DB-0C5D-BD2F-F7EC-01E759C4A8E2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C27759DB-0C5D-BD2F-F7EC-01E759C4A8E2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C27759DB-0C5D-BD2F-F7EC-01E759C4A8E2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C27759DB-0C5D-BD2F-F7EC-01E759C4A8E2}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{740943F5-2E99-4ED4-9022-BC14F480C3F8} = {3A04AA0A-A8A4-4ACC-A171-32EE16ADDC5C}
		{26E564DA-4315-44EF-B287-A9B3BC87BF1C} = {3A04AA0A-A8A4-4ACC-A171-32EE16ADDC5C}
		{9FA8F699-79F5-48DB-A2D9-717CE875C5F0} = {9B71283D-A4A6-480E-9A9B-AD446F08CCC5}
		{BF454D87-C790-4E9E-8E32-772DF3CA65BF} = {9FA8F699-79F5-48DB-A2D9-717CE875C5F0}
		{63D6491B-37EA-4F36-8B9D-2AD526F74BB8} = {9B71283D-A4A6-480E-9A9B-AD446F08CCC5}
		{8E651CB1-4524-4A5B-A47C-C1A51C73C775} = {63D6491B-37EA-4F36-8B9D-2AD526F74BB8}
		{5890241C-A3EF-4CEE-BA67-BA4EFD57E80C} = {63D6491B-37EA-4F36-8B9D-2AD526F74BB8}
		{CFF4EEBA-0EA7-49D1-B4F2-17DF640F2174} = {9FA8F699-79F5-48DB-A2D9-717CE875C5F0}
		{705D949C-2C10-46AD-9007-37E62BC8FA7A} = {3A04AA0A-A8A4-4ACC-A171-32EE16ADDC5C}
		{66AC78D8-0A73-41FC-925C-E2840EA2639F} = {705D949C-2C10-46AD-9007-37E62BC8FA7A}
		{53F78F6D-A2D0-41D5-8A81-7D71C5763249} = {3A04AA0A-A8A4-4ACC-A171-32EE16ADDC5C}
		{29322FD8-3BA2-47E3-A8D9-E9C918979670} = {53F78F6D-A2D0-41D5-8A81-7D71C5763249}
		{045EF11F-5494-426E-B7B3-660F1FCF23D5} = {3A04AA0A-A8A4-4ACC-A171-32EE16ADDC5C}
		{E289961D-B4F4-4368-9402-A0057A009122} = {53F78F6D-A2D0-41D5-8A81-7D71C5763249}
		{1DF10122-622E-4A4C-93C7-6172997D8332} = {3A04AA0A-A8A4-4ACC-A171-32EE16ADDC5C}
		{3EF85AAC-0972-46C4-BABE-FF568E7A9BE7} = {3A04AA0A-A8A4-4ACC-A171-32EE16ADDC5C}
		{069C0BDF-E992-443B-A1C0-216F18216BFE} = {3EF85AAC-0972-46C4-BABE-FF568E7A9BE7}
		{73A019ED-4595-4E33-A95A-F502071E32D7} = {3EF85AAC-0972-46C4-BABE-FF568E7A9BE7}
		{2383D6A4-790C-4661-A5A4-70C051F3DDD4} = {634ED71E-3E71-4F43-B9A2-11536F91EAA8}
		{11E03881-DB12-47D2-B517-BAA3D0CCE7F6} = {2383D6A4-790C-4661-A5A4-70C051F3DDD4}
		{56BE1E11-9CC5-4F03-807F-E77A5F7710C3} = {2383D6A4-790C-4661-A5A4-70C051F3DDD4}
		{94911763-A899-4C80-87C8-61CF09448DCB} = {2383D6A4-790C-4661-A5A4-70C051F3DDD4}
		{C27759DB-0C5D-BD2F-F7EC-01E759C4A8E2} = {3A04AA0A-A8A4-4ACC-A171-32EE16ADDC5C}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {5981247F-FDA6-460F-8B83-6AFBEE114226}
	EndGlobalSection
EndGlobal
