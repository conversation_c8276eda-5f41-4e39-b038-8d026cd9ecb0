<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
	  <TargetFramework>net9.0-windows</TargetFramework>
    <LangVersion>13.0</LangVersion>
    <Nullable>enable</Nullable>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.CodeAnalysis" Version="4.4.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\external\example-framework\src\ExampleFramework\ExampleFramework.csproj" />
    <ProjectReference Include="..\..\..\src\UniversalUI.Analyzers\UniversalUI.Analyzers.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
    <ProjectReference Include="..\..\..\src\wpf\UniversalUI.FrameworkForwarder\UniversalUI.FrameworkForwarder-wpf.csproj" />
    <ProjectReference Include="..\..\controls\Microcharts\Microcharts.csproj" />
    <ProjectReference Include="..\..\controls\SimpleControls\SimpleControls.csproj" />
  </ItemGroup>

</Project>