<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <GeneratePackageOnBuild>false</GeneratePackageOnBuild>
    <Version>0.9.0</Version>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <PackageProjectUrl>https://github.com/aloisdeniel/Microcharts/</PackageProjectUrl>
    <PackageLicenseUrl>https://github.com/aloisdeniel/Microcharts/blob/master/LICENSE</PackageLicenseUrl>
    <RepositoryUrl>https://github.com/aloisdeniel/Microcharts/</RepositoryUrl>
    <RepositoryType>GitHub</RepositoryType>
    <Description>Microcharts is an extremely simple charting library for a wide range of platforms (see Compatibility section below), with shared code and rendering for all of them!</Description>
    <Authors><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON></Authors>
    <PackageTags>xamarin ios android chart skia</PackageTags>
    <PackageRequireLicenseAcceptance>false</PackageRequireLicenseAcceptance>
    <Company>Microcharts</Company>
    <Copyright>Copyright 2020</Copyright>
    <PackageIconUrl>https://raw.githubusercontent.com/aloisdeniel/Microcharts/master/Documentation/Logo.png</PackageIconUrl>
    <TargetFramework>netstandard2.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="SkiaSharp" Version="2.88.3" />
    <PackageReference Include="SkiaSharp.HarfBuzz" Version="2.88.3" />
    <PackageReference Include="System.ValueTuple" Version="4.5.0" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="**\.DS_Store" CopyToPublishDirectory="Never" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Microcharts.nuspec" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\external\example-framework\src\ExampleFramework\ExampleFramework.csproj" />
    <ProjectReference Include="..\..\..\src\UniversalUI.Analyzers\UniversalUI.Analyzers.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
    <ProjectReference Include="..\..\..\src\UniversalUI.FrameworkForwarderStub\UniversalUI.FrameworkForwarder-reference.csproj" Private="False" PrivateAssets="All" />
  </ItemGroup>

</Project>
