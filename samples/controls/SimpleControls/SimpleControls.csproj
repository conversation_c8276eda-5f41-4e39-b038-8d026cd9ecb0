<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
	  <LangVersion>8.0</LangVersion>
	  <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\external\example-framework\src\ExampleFramework\ExampleFramework.csproj" />
    <ProjectReference Include="..\..\..\src\UniversalUI.Analyzers\UniversalUI.Analyzers.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="False" />
    <ProjectReference Include="..\..\..\src\UniversalUI.FrameworkForwarderStub\UniversalUI.FrameworkForwarder-reference.csproj" Private="False" PrivateAssets="All" />
  </ItemGroup>

  <ItemGroup Condition="$(UseLocalExampleFramework) == ''">
  </ItemGroup>

</Project>
