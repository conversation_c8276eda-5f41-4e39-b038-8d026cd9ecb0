# Recursively get all .cs files in the current directory and subdirectories
$files = Get-ChildItem -Path . -Filter *.cs -Recurse

foreach ($file in $files) {
    try {
        # Read file content
        $content = Get-Content -Path $file.FullName -Raw
        $hasUnoComment = $content -match "// This file is copied, with modifications, from the Uno project"
        $hasNullableEnable = $content -match "#nullable enable"
        
        if ($hasUnoComment -and -not $hasNullableEnable) {
            Write-Host "Updating $($file.FullName)"
            
            # Split the file content into lines
            $lines = Get-Content -Path $file.FullName
            
            # Find the line number of the Uno comment
            $commentLineNumber = 0
            for ($i = 0; $i -lt $lines.Count; $i++) {
                if ($lines[$i] -match "// This file is copied, with modifications, from the Uno project") {
                    $commentLineNumber = $i
                    break
                }
            }
            
            # Insert #nullable disable followed by a blank line, after the comment and its blank line
            if ($commentLineNumber -ge 0 -and $commentLineNumber + 1 -lt $lines.Count -and $lines[$commentLineNumber + 1].Trim() -eq "") {
                $updatedContent = @()
                $updatedContent += $lines[0..$commentLineNumber]
                $updatedContent += $lines[$commentLineNumber + 1]  # Keep the existing blank line
                $updatedContent += "#nullable disable"
                $updatedContent += ""  # Add one blank line after #nullable disable
                $updatedContent += $lines[($commentLineNumber + 2)..($lines.Count - 1)]
                
                # Save the modified content back to the file with the original encoding
                $updatedContent | Out-File -FilePath $file.FullName -Encoding utf8
                
                Write-Host "  Successfully updated $($file.Name)"
            }
            else {
                Write-Host "  Skipping $($file.Name) - Could not find blank line after Uno comment" -ForegroundColor Yellow
            }
        }
    }
    catch {
        Write-Host "  Error processing $($file.FullName): $_" -ForegroundColor Red
    }
}

Write-Host "Finished updating files." -ForegroundColor Green

