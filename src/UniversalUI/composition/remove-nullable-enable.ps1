# Recursively get all .cs files in the current directory and subdirectories
$files = Get-ChildItem -Path . -Filter *.cs -Recurse

foreach ($file in $files) {
    try {
        # Read file content
        $content = Get-Content -Path $file.FullName -Raw
        
        # Check if the file contains #nullable enable
        if ($content -match "#nullable enable") {
            Write-Host "Processing $($file.FullName)"
            
            # Read file line by line
            $lines = Get-Content -Path $file.FullName
            $updatedContent = @()
            $skipMode = $false
            $lineIndex = 0
            
            while ($lineIndex -lt $lines.Count) {
                $line = $lines[$lineIndex]
                
                # Check if this line contains #nullable enable
                if ($line -match "#nullable enable") {
                    $skipMode = $true
                    $lineIndex++
                    
                    # Skip any blank lines that follow
                    while ($lineIndex -lt $lines.Count -and $lines[$lineIndex].Trim() -eq "") {
                        $lineIndex++
                    }
                    
                    # Continue with the next iteration without adding this line
                    continue
                }
                
                # Add the line to our updated content
                $updatedContent += $line
                $lineIndex++
            }
            
            # Save the modified content back to the file with the original encoding
            $updatedContent | Out-File -FilePath $file.FullName -Encoding utf8
            
            Write-Host "  Successfully updated $($file.Name)"
        }
    }
    catch {
        Write-Host "  Error processing $($file.FullName): $_" -ForegroundColor Red
    }
}

Write-Host "Finished removing #nullable enable directives." -ForegroundColor Green

