// This file is copied, with modifications, from the Uno project

#nullable disable

namespace UniversalUI.Composition;

public partial class DelegatedInkTrailVisual : Visual
{
#pragma warning disable IDE0051 // Private member 'DelegatedInkTrailVisual..ctor' is unused - Required constructor for base class
	private DelegatedInkTrailVisual(Compositor compositor) : base(compositor)
#pragma warning restore IDE0051 // Private member 'DelegatedInkTrailVisual..ctor' is unused
	{
	}
}
