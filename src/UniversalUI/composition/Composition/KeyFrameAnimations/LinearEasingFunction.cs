// This file is copied, with modifications, from the Uno project

#nullable disable

using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;

namespace UniversalUI.Composition;

public partial class LinearEasingFunction
{
	internal LinearEasingFunction(Compositor owner) : base(owner) { }
}
