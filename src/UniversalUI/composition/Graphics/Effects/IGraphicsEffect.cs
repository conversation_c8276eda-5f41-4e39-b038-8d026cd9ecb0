// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Graphics.Effects
{
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
	[global::UniversalUI.NotImplemented]
#endif
	public partial interface IGraphicsEffect : global::UniversalUI.Graphics.Effects.IGraphicsEffectSource
	{
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		string Name
		{
			get;
			set;
		}
#endif
		// Forced skipping of method Windows.Graphics.Effects.IGraphicsEffect.Name.get
		// Forced skipping of method Windows.Graphics.Effects.IGraphicsEffect.Name.set
	}
}
