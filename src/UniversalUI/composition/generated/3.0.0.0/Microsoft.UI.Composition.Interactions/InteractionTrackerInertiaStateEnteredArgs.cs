// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition.Interactions
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class InteractionTrackerInertiaStateEnteredArgs
	{
		// Skipping already declared property ModifiedRestingPosition
		// Skipping already declared property ModifiedRestingScale
		// Skipping already declared property NaturalRestingPosition
		// Skipping already declared property NaturalRestingScale
		// Skipping already declared property PositionVelocityInPixelsPerSecond
		// Skipping already declared property RequestId
		// Skipping already declared property ScaleVelocityInPercentPerSecond
		// Skipping already declared property IsInertiaFromImpulse
		// Skipping already declared property IsFromBinding
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTrackerInertiaStateEnteredArgs.ModifiedRestingScale.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTrackerInertiaStateEnteredArgs.NaturalRestingScale.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTrackerInertiaStateEnteredArgs.PositionVelocityInPixelsPerSecond.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTrackerInertiaStateEnteredArgs.ModifiedRestingPosition.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTrackerInertiaStateEnteredArgs.IsFromBinding.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTrackerInertiaStateEnteredArgs.NaturalRestingPosition.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTrackerInertiaStateEnteredArgs.RequestId.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTrackerInertiaStateEnteredArgs.ScaleVelocityInPercentPerSecond.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTrackerInertiaStateEnteredArgs.IsInertiaFromImpulse.get
	}
}
