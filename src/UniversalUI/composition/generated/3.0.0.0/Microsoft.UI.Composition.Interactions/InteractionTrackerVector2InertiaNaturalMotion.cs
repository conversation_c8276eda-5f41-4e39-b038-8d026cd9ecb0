// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition.Interactions
{
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
	[global::UniversalUI.NotImplemented]
#endif
	public partial class InteractionTrackerVector2InertiaNaturalMotion : global::UniversalUI.Composition.Interactions.InteractionTrackerVector2InertiaModifier
	{
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		internal InteractionTrackerVector2InertiaNaturalMotion()
		{
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::UniversalUI.Composition.ExpressionAnimation Condition
		{
			get
			{
				throw new global::System.NotImplementedException("The member ExpressionAnimation InteractionTrackerVector2InertiaNaturalMotion.Condition is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=ExpressionAnimation%20InteractionTrackerVector2InertiaNaturalMotion.Condition");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Interactions.InteractionTrackerVector2InertiaNaturalMotion", "ExpressionAnimation InteractionTrackerVector2InertiaNaturalMotion.Condition");
			}
		}
#endif
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTrackerVector2InertiaNaturalMotion.Condition.set
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTrackerVector2InertiaNaturalMotion.NaturalMotion.set
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTrackerVector2InertiaNaturalMotion.Condition.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTrackerVector2InertiaNaturalMotion.NaturalMotion.get
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public static global::UniversalUI.Composition.Interactions.InteractionTrackerVector2InertiaNaturalMotion Create(global::UniversalUI.Composition.Compositor compositor)
		{
			throw new global::System.NotImplementedException("The member InteractionTrackerVector2InertiaNaturalMotion InteractionTrackerVector2InertiaNaturalMotion.Create(Compositor compositor) is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=InteractionTrackerVector2InertiaNaturalMotion%20InteractionTrackerVector2InertiaNaturalMotion.Create%28Compositor%20compositor%29");
		}
#endif
	}
}
