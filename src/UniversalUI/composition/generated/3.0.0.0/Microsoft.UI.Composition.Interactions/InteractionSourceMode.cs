// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition.Interactions
{
#if false || false || false || false || false || false || false
	public enum InteractionSourceMode
	{
		// Skipping already declared field UniversalUI.Composition.Interactions.InteractionSourceMode.Disabled
		// Skipping already declared field UniversalUI.Composition.Interactions.InteractionSourceMode.EnabledWithInertia
		// Skipping already declared field UniversalUI.Composition.Interactions.InteractionSourceMode.EnabledWithoutInertia
	}
#endif
}
