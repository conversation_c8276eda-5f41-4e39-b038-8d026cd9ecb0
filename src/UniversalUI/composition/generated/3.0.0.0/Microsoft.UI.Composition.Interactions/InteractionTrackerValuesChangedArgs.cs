// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition.Interactions
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class InteractionTrackerValuesChangedArgs
	{
		// Skipping already declared property Position
		// Skipping already declared property RequestId
		// Skipping already declared property Scale
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTrackerValuesChangedArgs.RequestId.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTrackerValuesChangedArgs.Scale.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTrackerValuesChangedArgs.Position.get
	}
}
