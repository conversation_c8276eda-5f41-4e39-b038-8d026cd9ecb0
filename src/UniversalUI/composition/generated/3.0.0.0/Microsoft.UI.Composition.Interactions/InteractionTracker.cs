// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition.Interactions
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class InteractionTracker : global::UniversalUI.Composition.CompositionObject
	{
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public float? ScaleInertiaDecayRate
		{
			get
			{
				throw new global::System.NotImplementedException("The member float? InteractionTracker.ScaleInertiaDecayRate is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=float%3F%20InteractionTracker.ScaleInertiaDecayRate");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Interactions.InteractionTracker", "float? InteractionTracker.ScaleInertiaDecayRate");
			}
		}
#endif
		// Skipping already declared property PositionInertiaDecayRate
		// Skipping already declared property MinScale
		// Skipping already declared property MinPosition
		// Skipping already declared property MaxScale
		// Skipping already declared property MaxPosition
		// Skipping already declared property InteractionSources
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public bool IsPositionRoundingSuggested
		{
			get
			{
				throw new global::System.NotImplementedException("The member bool InteractionTracker.IsPositionRoundingSuggested is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=bool%20InteractionTracker.IsPositionRoundingSuggested");
			}
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::System.Numerics.Vector3 NaturalRestingPosition
		{
			get
			{
				throw new global::System.NotImplementedException("The member Vector3 InteractionTracker.NaturalRestingPosition is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=Vector3%20InteractionTracker.NaturalRestingPosition");
			}
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public float NaturalRestingScale
		{
			get
			{
				throw new global::System.NotImplementedException("The member float InteractionTracker.NaturalRestingScale is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=float%20InteractionTracker.NaturalRestingScale");
			}
		}
#endif
		// Skipping already declared property Owner
		// Skipping already declared property Position
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::System.Numerics.Vector3 PositionVelocityInPixelsPerSecond
		{
			get
			{
				throw new global::System.NotImplementedException("The member Vector3 InteractionTracker.PositionVelocityInPixelsPerSecond is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=Vector3%20InteractionTracker.PositionVelocityInPixelsPerSecond");
			}
		}
#endif
		// Skipping already declared property Scale
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public float ScaleVelocityInPercentPerSecond
		{
			get
			{
				throw new global::System.NotImplementedException("The member float InteractionTracker.ScaleVelocityInPercentPerSecond is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=float%20InteractionTracker.ScaleVelocityInPercentPerSecond");
			}
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public bool IsInertiaFromImpulse
		{
			get
			{
				throw new global::System.NotImplementedException("The member bool InteractionTracker.IsInertiaFromImpulse is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=bool%20InteractionTracker.IsInertiaFromImpulse");
			}
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public int TryUpdatePosition(global::System.Numerics.Vector3 value, global::UniversalUI.Composition.Interactions.InteractionTrackerClampingOption option, global::UniversalUI.Composition.Interactions.InteractionTrackerPositionUpdateOption posUpdateOption)
		{
			throw new global::System.NotImplementedException("The member int InteractionTracker.TryUpdatePosition(Vector3 value, InteractionTrackerClampingOption option, InteractionTrackerPositionUpdateOption posUpdateOption) is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=int%20InteractionTracker.TryUpdatePosition%28Vector3%20value%2C%20InteractionTrackerClampingOption%20option%2C%20InteractionTrackerPositionUpdateOption%20posUpdateOption%29");
		}
#endif
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTracker.IsInertiaFromImpulse.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTracker.InteractionSources.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTracker.PositionInertiaDecayRate.set
		// Skipping already declared method UniversalUI.Composition.Interactions.InteractionTracker.TryUpdatePositionBy(System.Numerics.Vector3, UniversalUI.Composition.Interactions.InteractionTrackerClampingOption)
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTracker.IsPositionRoundingSuggested.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTracker.MaxPosition.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTracker.MaxPosition.set
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTracker.MaxScale.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTracker.MaxScale.set
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTracker.MinPosition.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTracker.MinPosition.set
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTracker.MinScale.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTracker.MinScale.set
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTracker.NaturalRestingPosition.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTracker.NaturalRestingScale.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTracker.Owner.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTracker.Position.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTracker.PositionInertiaDecayRate.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTracker.PositionVelocityInPixelsPerSecond.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTracker.Scale.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTracker.ScaleInertiaDecayRate.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTracker.ScaleInertiaDecayRate.set
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTracker.ScaleVelocityInPercentPerSecond.get
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public void AdjustPositionXIfGreaterThanThreshold(float adjustment, float positionThreshold)
		{
			global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Interactions.InteractionTracker", "void InteractionTracker.AdjustPositionXIfGreaterThanThreshold(float adjustment, float positionThreshold)");
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public void AdjustPositionYIfGreaterThanThreshold(float adjustment, float positionThreshold)
		{
			global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Interactions.InteractionTracker", "void InteractionTracker.AdjustPositionYIfGreaterThanThreshold(float adjustment, float positionThreshold)");
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public void ConfigurePositionXInertiaModifiers(global::System.Collections.Generic.IEnumerable<global::UniversalUI.Composition.Interactions.InteractionTrackerInertiaModifier> modifiers)
		{
			global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Interactions.InteractionTracker", "void InteractionTracker.ConfigurePositionXInertiaModifiers(IEnumerable<InteractionTrackerInertiaModifier> modifiers)");
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public void ConfigurePositionYInertiaModifiers(global::System.Collections.Generic.IEnumerable<global::UniversalUI.Composition.Interactions.InteractionTrackerInertiaModifier> modifiers)
		{
			global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Interactions.InteractionTracker", "void InteractionTracker.ConfigurePositionYInertiaModifiers(IEnumerable<InteractionTrackerInertiaModifier> modifiers)");
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public void ConfigureScaleInertiaModifiers(global::System.Collections.Generic.IEnumerable<global::UniversalUI.Composition.Interactions.InteractionTrackerInertiaModifier> modifiers)
		{
			global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Interactions.InteractionTracker", "void InteractionTracker.ConfigureScaleInertiaModifiers(IEnumerable<InteractionTrackerInertiaModifier> modifiers)");
		}
#endif
		// Skipping already declared method UniversalUI.Composition.Interactions.InteractionTracker.TryUpdatePosition(System.Numerics.Vector3)
		// Skipping already declared method UniversalUI.Composition.Interactions.InteractionTracker.TryUpdatePositionBy(System.Numerics.Vector3)
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public int TryUpdatePositionWithAnimation(global::UniversalUI.Composition.CompositionAnimation animation)
		{
			throw new global::System.NotImplementedException("The member int InteractionTracker.TryUpdatePositionWithAnimation(CompositionAnimation animation) is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=int%20InteractionTracker.TryUpdatePositionWithAnimation%28CompositionAnimation%20animation%29");
		}
#endif
		// Skipping already declared method UniversalUI.Composition.Interactions.InteractionTracker.TryUpdatePositionWithAdditionalVelocity(System.Numerics.Vector3)
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public int TryUpdateScale(float value, global::System.Numerics.Vector3 centerPoint)
		{
			throw new global::System.NotImplementedException("The member int InteractionTracker.TryUpdateScale(float value, Vector3 centerPoint) is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=int%20InteractionTracker.TryUpdateScale%28float%20value%2C%20Vector3%20centerPoint%29");
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public int TryUpdateScaleWithAnimation(global::UniversalUI.Composition.CompositionAnimation animation, global::System.Numerics.Vector3 centerPoint)
		{
			throw new global::System.NotImplementedException("The member int InteractionTracker.TryUpdateScaleWithAnimation(CompositionAnimation animation, Vector3 centerPoint) is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=int%20InteractionTracker.TryUpdateScaleWithAnimation%28CompositionAnimation%20animation%2C%20Vector3%20centerPoint%29");
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public int TryUpdateScaleWithAdditionalVelocity(float velocityInPercentPerSecond, global::System.Numerics.Vector3 centerPoint)
		{
			throw new global::System.NotImplementedException("The member int InteractionTracker.TryUpdateScaleWithAdditionalVelocity(float velocityInPercentPerSecond, Vector3 centerPoint) is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=int%20InteractionTracker.TryUpdateScaleWithAdditionalVelocity%28float%20velocityInPercentPerSecond%2C%20Vector3%20centerPoint%29");
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public void ConfigureCenterPointXInertiaModifiers(global::System.Collections.Generic.IEnumerable<global::UniversalUI.Composition.Interactions.CompositionConditionalValue> conditionalValues)
		{
			global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Interactions.InteractionTracker", "void InteractionTracker.ConfigureCenterPointXInertiaModifiers(IEnumerable<CompositionConditionalValue> conditionalValues)");
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public void ConfigureCenterPointYInertiaModifiers(global::System.Collections.Generic.IEnumerable<global::UniversalUI.Composition.Interactions.CompositionConditionalValue> conditionalValues)
		{
			global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Interactions.InteractionTracker", "void InteractionTracker.ConfigureCenterPointYInertiaModifiers(IEnumerable<CompositionConditionalValue> conditionalValues)");
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public void ConfigureVector2PositionInertiaModifiers(global::System.Collections.Generic.IEnumerable<global::UniversalUI.Composition.Interactions.InteractionTrackerVector2InertiaModifier> modifiers)
		{
			global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Interactions.InteractionTracker", "void InteractionTracker.ConfigureVector2PositionInertiaModifiers(IEnumerable<InteractionTrackerVector2InertiaModifier> modifiers)");
		}
#endif
		// Skipping already declared method UniversalUI.Composition.Interactions.InteractionTracker.TryUpdatePosition(System.Numerics.Vector3, UniversalUI.Composition.Interactions.InteractionTrackerClampingOption)
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public static void SetBindingMode(global::UniversalUI.Composition.Interactions.InteractionTracker boundTracker1, global::UniversalUI.Composition.Interactions.InteractionTracker boundTracker2, global::UniversalUI.Composition.Interactions.InteractionBindingAxisModes axisMode)
		{
			global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Interactions.InteractionTracker", "void InteractionTracker.SetBindingMode(InteractionTracker boundTracker1, InteractionTracker boundTracker2, InteractionBindingAxisModes axisMode)");
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public static global::UniversalUI.Composition.Interactions.InteractionBindingAxisModes GetBindingMode(global::UniversalUI.Composition.Interactions.InteractionTracker boundTracker1, global::UniversalUI.Composition.Interactions.InteractionTracker boundTracker2)
		{
			throw new global::System.NotImplementedException("The member InteractionBindingAxisModes InteractionTracker.GetBindingMode(InteractionTracker boundTracker1, InteractionTracker boundTracker2) is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=InteractionBindingAxisModes%20InteractionTracker.GetBindingMode%28InteractionTracker%20boundTracker1%2C%20InteractionTracker%20boundTracker2%29");
		}
#endif
		// Skipping already declared method UniversalUI.Composition.Interactions.InteractionTracker.Create(UniversalUI.Composition.Compositor)
		// Skipping already declared method UniversalUI.Composition.Interactions.InteractionTracker.CreateWithOwner(UniversalUI.Composition.Compositor, UniversalUI.Composition.Interactions.IInteractionTrackerOwner)
	}
}
