// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition.Interactions
{
#if false || false || false || false || false || false || false
	public enum VisualInteractionSourceRedirectionMode
	{
		// Skipping already declared field UniversalUI.Composition.Interactions.VisualInteractionSourceRedirectionMode.Off
		// Skipping already declared field UniversalUI.Composition.Interactions.VisualInteractionSourceRedirectionMode.CapableTouchpadOnly
		// Skipping already declared field UniversalUI.Composition.Interactions.VisualInteractionSourceRedirectionMode.PointerWheelOnly
		// Skipping already declared field UniversalUI.Composition.Interactions.VisualInteractionSourceRedirectionMode.CapableTouchpadAndPointerWheel
	}
#endif
}
