// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition.Interactions
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial interface IInteractionTrackerOwner
	{
		// Skipping already declared method UniversalUI.Composition.Interactions.IInteractionTrackerOwner.CustomAnimationStateEntered(UniversalUI.Composition.Interactions.InteractionTracker, UniversalUI.Composition.Interactions.InteractionTrackerCustomAnimationStateEnteredArgs)
		// Skipping already declared method UniversalUI.Composition.Interactions.IInteractionTrackerOwner.IdleStateEntered(UniversalUI.Composition.Interactions.InteractionTracker, UniversalUI.Composition.Interactions.InteractionTrackerIdleStateEnteredArgs)
		// Skipping already declared method UniversalUI.Composition.Interactions.IInteractionTrackerOwner.InertiaStateEntered(UniversalUI.Composition.Interactions.InteractionTracker, UniversalUI.Composition.Interactions.InteractionTrackerInertiaStateEnteredArgs)
		// Skipping already declared method UniversalUI.Composition.Interactions.IInteractionTrackerOwner.InteractingStateEntered(UniversalUI.Composition.Interactions.InteractionTracker, UniversalUI.Composition.Interactions.InteractionTrackerInteractingStateEnteredArgs)
		// Skipping already declared method UniversalUI.Composition.Interactions.IInteractionTrackerOwner.RequestIgnored(UniversalUI.Composition.Interactions.InteractionTracker, UniversalUI.Composition.Interactions.InteractionTrackerRequestIgnoredArgs)
		// Skipping already declared method UniversalUI.Composition.Interactions.IInteractionTrackerOwner.ValuesChanged(UniversalUI.Composition.Interactions.InteractionTracker, UniversalUI.Composition.Interactions.InteractionTrackerValuesChangedArgs)
	}
}
