// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition.Interactions
{
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
	[global::UniversalUI.NotImplemented]
#endif
	public partial class InteractionSourceConfiguration : global::UniversalUI.Composition.CompositionObject
	{
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		internal InteractionSourceConfiguration()
		{
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::UniversalUI.Composition.Interactions.InteractionSourceRedirectionMode ScaleSourceMode
		{
			get
			{
				throw new global::System.NotImplementedException("The member InteractionSourceRedirectionMode InteractionSourceConfiguration.ScaleSourceMode is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=InteractionSourceRedirectionMode%20InteractionSourceConfiguration.ScaleSourceMode");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Interactions.InteractionSourceConfiguration", "InteractionSourceRedirectionMode InteractionSourceConfiguration.ScaleSourceMode");
			}
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::UniversalUI.Composition.Interactions.InteractionSourceRedirectionMode PositionYSourceMode
		{
			get
			{
				throw new global::System.NotImplementedException("The member InteractionSourceRedirectionMode InteractionSourceConfiguration.PositionYSourceMode is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=InteractionSourceRedirectionMode%20InteractionSourceConfiguration.PositionYSourceMode");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Interactions.InteractionSourceConfiguration", "InteractionSourceRedirectionMode InteractionSourceConfiguration.PositionYSourceMode");
			}
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::UniversalUI.Composition.Interactions.InteractionSourceRedirectionMode PositionXSourceMode
		{
			get
			{
				throw new global::System.NotImplementedException("The member InteractionSourceRedirectionMode InteractionSourceConfiguration.PositionXSourceMode is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=InteractionSourceRedirectionMode%20InteractionSourceConfiguration.PositionXSourceMode");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Interactions.InteractionSourceConfiguration", "InteractionSourceRedirectionMode InteractionSourceConfiguration.PositionXSourceMode");
			}
		}
#endif
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionSourceConfiguration.PositionXSourceMode.set
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionSourceConfiguration.PositionXSourceMode.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionSourceConfiguration.PositionYSourceMode.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionSourceConfiguration.PositionYSourceMode.set
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionSourceConfiguration.ScaleSourceMode.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionSourceConfiguration.ScaleSourceMode.set
	}
}
