// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition.Interactions
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class VisualInteractionSource : global::UniversalUI.Composition.CompositionObject, global::UniversalUI.Composition.Interactions.ICompositionInteractionSource
	{
		// Skipping already declared property ScaleSourceMode
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::UniversalUI.Composition.Interactions.InteractionChainingMode ScaleChainingMode
		{
			get
			{
				throw new global::System.NotImplementedException("The member InteractionChainingMode VisualInteractionSource.ScaleChainingMode is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=InteractionChainingMode%20VisualInteractionSource.ScaleChainingMode");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Interactions.VisualInteractionSource", "InteractionChainingMode VisualInteractionSource.ScaleChainingMode");
			}
		}
#endif
		// Skipping already declared property PositionYSourceMode
		// Skipping already declared property PositionYChainingMode
		// Skipping already declared property PositionXSourceMode
		// Skipping already declared property PositionXChainingMode
		// Skipping already declared property ManipulationRedirectionMode
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public bool IsPositionYRailsEnabled
		{
			get
			{
				throw new global::System.NotImplementedException("The member bool VisualInteractionSource.IsPositionYRailsEnabled is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=bool%20VisualInteractionSource.IsPositionYRailsEnabled");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Interactions.VisualInteractionSource", "bool VisualInteractionSource.IsPositionYRailsEnabled");
			}
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public bool IsPositionXRailsEnabled
		{
			get
			{
				throw new global::System.NotImplementedException("The member bool VisualInteractionSource.IsPositionXRailsEnabled is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=bool%20VisualInteractionSource.IsPositionXRailsEnabled");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Interactions.VisualInteractionSource", "bool VisualInteractionSource.IsPositionXRailsEnabled");
			}
		}
#endif
		// Skipping already declared property Source
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::System.Numerics.Vector3 DeltaPosition
		{
			get
			{
				throw new global::System.NotImplementedException("The member Vector3 VisualInteractionSource.DeltaPosition is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=Vector3%20VisualInteractionSource.DeltaPosition");
			}
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public float DeltaScale
		{
			get
			{
				throw new global::System.NotImplementedException("The member float VisualInteractionSource.DeltaScale is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=float%20VisualInteractionSource.DeltaScale");
			}
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::System.Numerics.Vector3 Position
		{
			get
			{
				throw new global::System.NotImplementedException("The member Vector3 VisualInteractionSource.Position is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=Vector3%20VisualInteractionSource.Position");
			}
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::System.Numerics.Vector3 PositionVelocity
		{
			get
			{
				throw new global::System.NotImplementedException("The member Vector3 VisualInteractionSource.PositionVelocity is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=Vector3%20VisualInteractionSource.PositionVelocity");
			}
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public float Scale
		{
			get
			{
				throw new global::System.NotImplementedException("The member float VisualInteractionSource.Scale is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=float%20VisualInteractionSource.Scale");
			}
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public float ScaleVelocity
		{
			get
			{
				throw new global::System.NotImplementedException("The member float VisualInteractionSource.ScaleVelocity is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=float%20VisualInteractionSource.ScaleVelocity");
			}
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::UniversalUI.Composition.Interactions.InteractionSourceConfiguration PointerWheelConfig
		{
			get
			{
				throw new global::System.NotImplementedException("The member InteractionSourceConfiguration VisualInteractionSource.PointerWheelConfig is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=InteractionSourceConfiguration%20VisualInteractionSource.PointerWheelConfig");
			}
		}
#endif
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.ManipulationRedirectionMode.get
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.PositionXSourceMode.set
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.PositionYChainingMode.get
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.PositionYChainingMode.set
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.PositionYSourceMode.get
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.PositionYSourceMode.set
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.PositionVelocity.get
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.Scale.get
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.IsPositionYRailsEnabled.set
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.ScaleChainingMode.get
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.ScaleChainingMode.set
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.ScaleSourceMode.get
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.ScaleSourceMode.set
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.Source.get
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.TryRedirectForManipulation(Microsoft.UI.Input.PointerPoint)
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.ManipulationRedirectionMode.set
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.DeltaPosition.get
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.DeltaScale.get
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.Position.get
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.ScaleVelocity.get
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public void ConfigureCenterPointXModifiers(global::System.Collections.Generic.IEnumerable<global::UniversalUI.Composition.Interactions.CompositionConditionalValue> conditionalValues)
		{
			global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Interactions.VisualInteractionSource", "void VisualInteractionSource.ConfigureCenterPointXModifiers(IEnumerable<CompositionConditionalValue> conditionalValues)");
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public void ConfigureCenterPointYModifiers(global::System.Collections.Generic.IEnumerable<global::UniversalUI.Composition.Interactions.CompositionConditionalValue> conditionalValues)
		{
			global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Interactions.VisualInteractionSource", "void VisualInteractionSource.ConfigureCenterPointYModifiers(IEnumerable<CompositionConditionalValue> conditionalValues)");
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public void ConfigureDeltaPositionXModifiers(global::System.Collections.Generic.IEnumerable<global::UniversalUI.Composition.Interactions.CompositionConditionalValue> conditionalValues)
		{
			global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Interactions.VisualInteractionSource", "void VisualInteractionSource.ConfigureDeltaPositionXModifiers(IEnumerable<CompositionConditionalValue> conditionalValues)");
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public void ConfigureDeltaPositionYModifiers(global::System.Collections.Generic.IEnumerable<global::UniversalUI.Composition.Interactions.CompositionConditionalValue> conditionalValues)
		{
			global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Interactions.VisualInteractionSource", "void VisualInteractionSource.ConfigureDeltaPositionYModifiers(IEnumerable<CompositionConditionalValue> conditionalValues)");
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public void ConfigureDeltaScaleModifiers(global::System.Collections.Generic.IEnumerable<global::UniversalUI.Composition.Interactions.CompositionConditionalValue> conditionalValues)
		{
			global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Interactions.VisualInteractionSource", "void VisualInteractionSource.ConfigureDeltaScaleModifiers(IEnumerable<CompositionConditionalValue> conditionalValues)");
		}
#endif
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.PointerWheelConfig.get
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.PositionXChainingMode.get
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.PositionXChainingMode.set
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.PositionXSourceMode.get
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.IsPositionXRailsEnabled.get
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.IsPositionXRailsEnabled.set
		// Forced skipping of method UniversalUI.Composition.Interactions.VisualInteractionSource.IsPositionYRailsEnabled.get
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public static global::UniversalUI.Composition.Interactions.VisualInteractionSource CreateFromIVisualElement(global::UniversalUI.Composition.IVisualElement source)
		{
			throw new global::System.NotImplementedException("The member VisualInteractionSource VisualInteractionSource.CreateFromIVisualElement(IVisualElement source) is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=VisualInteractionSource%20VisualInteractionSource.CreateFromIVisualElement%28IVisualElement%20source%29");
		}
#endif
		// Skipping already declared method UniversalUI.Composition.Interactions.VisualInteractionSource.Create(UniversalUI.Composition.Visual)
		// Processing: UniversalUI.Composition.Interactions.ICompositionInteractionSource
	}
}
