// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition.Interactions
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class InteractionTrackerInteractingStateEnteredArgs
	{
		// Skipping already declared property RequestId
		// Skipping already declared property IsFromBinding
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTrackerInteractingStateEnteredArgs.IsFromBinding.get
		// Forced skipping of method UniversalUI.Composition.Interactions.InteractionTrackerInteractingStateEnteredArgs.RequestId.get
	}
}
