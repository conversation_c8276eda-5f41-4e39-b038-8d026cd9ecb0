// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition.Interactions
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class CompositionInteractionSourceCollection : global::UniversalUI.Composition.CompositionObject, global::System.Collections.Generic.IEnumerable<global::UniversalUI.Composition.Interactions.ICompositionInteractionSource>
	{
		// Skipping already declared property Count
		// Skipping already declared method UniversalUI.Composition.Interactions.CompositionInteractionSourceCollection.RemoveAll()
		// Skipping already declared method UniversalUI.Composition.Interactions.CompositionInteractionSourceCollection.Add(UniversalUI.Composition.Interactions.ICompositionInteractionSource)
		// Skipping already declared method UniversalUI.Composition.Interactions.CompositionInteractionSourceCollection.Remove(UniversalUI.Composition.Interactions.ICompositionInteractionSource)
		// Forced skipping of method UniversalUI.Composition.Interactions.CompositionInteractionSourceCollection.Count.get
		// Forced skipping of method UniversalUI.Composition.Interactions.CompositionInteractionSourceCollection.First()
		// Processing: System.Collections.Generic.IEnumerable<UniversalUI.Composition.Interactions.ICompositionInteractionSource>
		// Processing: System.Collections.IEnumerable
	}
}
