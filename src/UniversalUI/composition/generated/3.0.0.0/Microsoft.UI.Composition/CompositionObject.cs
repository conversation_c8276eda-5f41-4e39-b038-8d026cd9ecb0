// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class CompositionObject : global::System.IDisposable, global::UniversalUI.Composition.IAnimationObject
	{
		// Skipping already declared property Compositor
		// Skipping already declared property Properties
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::UniversalUI.Composition.ImplicitAnimationCollection ImplicitAnimations
		{
			get
			{
				throw new global::System.NotImplementedException("The member ImplicitAnimationCollection CompositionObject.ImplicitAnimations is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=ImplicitAnimationCollection%20CompositionObject.ImplicitAnimations");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.CompositionObject", "ImplicitAnimationCollection CompositionObject.ImplicitAnimations");
			}
		}
#endif
		// Skipping already declared method UniversalUI.Composition.CompositionObject.Dispose()
		// Forced skipping of method UniversalUI.Composition.CompositionObject.Comment.set
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public void PopulatePropertyInfo(string propertyName, global::UniversalUI.Composition.AnimationPropertyInfo propertyInfo)
		{
			global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.CompositionObject", "void CompositionObject.PopulatePropertyInfo(string propertyName, AnimationPropertyInfo propertyInfo)");
		}
#endif
		// Forced skipping of method UniversalUI.Composition.CompositionObject.Properties.get
		// Skipping already declared method UniversalUI.Composition.CompositionObject.StartAnimation(string, UniversalUI.Composition.CompositionAnimation)
		// Skipping already declared method UniversalUI.Composition.CompositionObject.StopAnimation(string)
		// Forced skipping of method UniversalUI.Composition.CompositionObject.Comment.get
		// Forced skipping of method UniversalUI.Composition.CompositionObject.Compositor.get
		// Forced skipping of method UniversalUI.Composition.CompositionObject.ImplicitAnimations.get
		// Forced skipping of method UniversalUI.Composition.CompositionObject.ImplicitAnimations.set
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public void StartAnimationGroup(global::UniversalUI.Composition.ICompositionAnimationBase value)
		{
			global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.CompositionObject", "void CompositionObject.StartAnimationGroup(ICompositionAnimationBase value)");
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public void StopAnimationGroup(global::UniversalUI.Composition.ICompositionAnimationBase value)
		{
			global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.CompositionObject", "void CompositionObject.StopAnimationGroup(ICompositionAnimationBase value)");
		}
#endif
		// Forced skipping of method UniversalUI.Composition.CompositionObject.DispatcherQueue.get
		// Skipping already declared method UniversalUI.Composition.CompositionObject.TryGetAnimationController(string)
		// Skipping already declared method UniversalUI.Composition.CompositionObject.StartAnimation(string, UniversalUI.Composition.CompositionAnimation, UniversalUI.Composition.AnimationController)
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public static void StartAnimationWithIAnimationObject(global::UniversalUI.Composition.IAnimationObject target, string propertyName, global::UniversalUI.Composition.CompositionAnimation animation)
		{
			global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.CompositionObject", "void CompositionObject.StartAnimationWithIAnimationObject(IAnimationObject target, string propertyName, CompositionAnimation animation)");
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public static void StartAnimationGroupWithIAnimationObject(global::UniversalUI.Composition.IAnimationObject target, global::UniversalUI.Composition.ICompositionAnimationBase animation)
		{
			global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.CompositionObject", "void CompositionObject.StartAnimationGroupWithIAnimationObject(IAnimationObject target, ICompositionAnimationBase animation)");
		}
#endif
		// Processing: System.IDisposable
		// Processing: UniversalUI.Composition.IAnimationObject
	}
}
