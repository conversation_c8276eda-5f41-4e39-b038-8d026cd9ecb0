// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class InsetClip : global::UniversalUI.Composition.CompositionClip
	{
		// Skipping already declared property TopInset
		// Skipping already declared property RightInset
		// Skipping already declared property LeftInset
		// Skipping already declared property BottomInset
		// Forced skipping of method UniversalUI.Composition.InsetClip.TopInset.set
		// Forced skipping of method UniversalUI.Composition.InsetClip.BottomInset.get
		// Forced skipping of method UniversalUI.Composition.InsetClip.LeftInset.get
		// Forced skipping of method UniversalUI.Composition.InsetClip.LeftInset.set
		// Forced skipping of method UniversalUI.Composition.InsetClip.RightInset.get
		// Forced skipping of method UniversalUI.Composition.InsetClip.RightInset.set
		// Forced skipping of method UniversalUI.Composition.InsetClip.TopInset.get
		// Forced skipping of method UniversalUI.Composition.InsetClip.BottomInset.set
	}
}
