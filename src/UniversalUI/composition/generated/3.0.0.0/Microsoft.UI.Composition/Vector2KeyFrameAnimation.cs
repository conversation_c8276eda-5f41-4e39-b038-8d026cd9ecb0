// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class Vector2KeyFrameAnimation : global::UniversalUI.Composition.KeyFrameAnimation
	{
		// Skipping already declared method UniversalUI.Composition.Vector2KeyFrameAnimation.InsertKeyFrame(float, System.Numerics.Vector2, UniversalUI.Composition.CompositionEasingFunction)
		// Skipping already declared method UniversalUI.Composition.Vector2KeyFrameAnimation.InsertKeyFrame(float, System.Numerics.Vector2)
	}
}
