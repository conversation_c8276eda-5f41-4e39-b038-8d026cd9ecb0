// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class CompositionClip : global::UniversalUI.Composition.CompositionObject
	{
		// Skipping already declared property TransformMatrix
		// Skipping already declared property Scale
		// Skipping already declared property RotationAngleInDegrees
		// Skipping already declared property RotationAngle
		// Skipping already declared property Offset
		// Skipping already declared property CenterPoint
		// Skipping already declared property AnchorPoint
		// Forced skipping of method UniversalUI.Composition.CompositionClip.AnchorPoint.set
		// Forced skipping of method UniversalUI.Composition.CompositionClip.TransformMatrix.get
		// Forced skipping of method UniversalUI.Composition.CompositionClip.RotationAngle.get
		// Forced skipping of method UniversalUI.Composition.CompositionClip.Scale.get
		// Forced skipping of method UniversalUI.Composition.CompositionClip.CenterPoint.get
		// Forced skipping of method UniversalUI.Composition.CompositionClip.CenterPoint.set
		// Forced skipping of method UniversalUI.Composition.CompositionClip.Offset.get
		// Forced skipping of method UniversalUI.Composition.CompositionClip.Offset.set
		// Forced skipping of method UniversalUI.Composition.CompositionClip.RotationAngleInDegrees.set
		// Forced skipping of method UniversalUI.Composition.CompositionClip.RotationAngle.set
		// Forced skipping of method UniversalUI.Composition.CompositionClip.RotationAngleInDegrees.get
		// Forced skipping of method UniversalUI.Composition.CompositionClip.Scale.set
		// Forced skipping of method UniversalUI.Composition.CompositionClip.TransformMatrix.set
		// Forced skipping of method UniversalUI.Composition.CompositionClip.AnchorPoint.get
	}
}
