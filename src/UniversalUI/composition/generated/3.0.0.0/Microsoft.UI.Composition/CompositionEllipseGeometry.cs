// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class CompositionEllipseGeometry : global::UniversalUI.Composition.CompositionGeometry
	{
		// Skipping already declared property Radius
		// Skipping already declared property Center
		// Forced skipping of method UniversalUI.Composition.CompositionEllipseGeometry.Radius.set
		// Forced skipping of method UniversalUI.Composition.CompositionEllipseGeometry.Center.get
		// Forced skipping of method UniversalUI.Composition.CompositionEllipseGeometry.Radius.get
		// Forced skipping of method UniversalUI.Composition.CompositionEllipseGeometry.Center.set
	}
}
