// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class CompositionColorGradientStopCollection : global::System.Collections.Generic.IEnumerable<global::UniversalUI.Composition.CompositionColorGradientStop>, global::System.Collections.Generic.IList<global::UniversalUI.Composition.CompositionColorGradientStop>
	{
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public uint Size
		{
			get
			{
				throw new global::System.NotImplementedException("The member uint CompositionColorGradientStopCollection.Size is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=uint%20CompositionColorGradientStopCollection.Size");
			}
		}
#endif
		// Forced skipping of method UniversalUI.Composition.CompositionColorGradientStopCollection.IndexOf(UniversalUI.Composition.CompositionColorGradientStop, out uint)
		// Forced skipping of method UniversalUI.Composition.CompositionColorGradientStopCollection.InsertAt(uint, UniversalUI.Composition.CompositionColorGradientStop)
		// Forced skipping of method UniversalUI.Composition.CompositionColorGradientStopCollection.GetAt(uint)
		// Forced skipping of method UniversalUI.Composition.CompositionColorGradientStopCollection.First()
		// Forced skipping of method UniversalUI.Composition.CompositionColorGradientStopCollection.Size.get
		// Forced skipping of method UniversalUI.Composition.CompositionColorGradientStopCollection.GetView()
		// Forced skipping of method UniversalUI.Composition.CompositionColorGradientStopCollection.RemoveAtEnd()
		// Forced skipping of method UniversalUI.Composition.CompositionColorGradientStopCollection.ReplaceAll(UniversalUI.Composition.CompositionColorGradientStop[])
		// Forced skipping of method UniversalUI.Composition.CompositionColorGradientStopCollection.RemoveAt(uint)
		// Forced skipping of method UniversalUI.Composition.CompositionColorGradientStopCollection.Append(UniversalUI.Composition.CompositionColorGradientStop)
		// Forced skipping of method UniversalUI.Composition.CompositionColorGradientStopCollection.SetAt(uint, UniversalUI.Composition.CompositionColorGradientStop)
		// Forced skipping of method UniversalUI.Composition.CompositionColorGradientStopCollection.Clear()
		// Forced skipping of method UniversalUI.Composition.CompositionColorGradientStopCollection.GetMany(uint, UniversalUI.Composition.CompositionColorGradientStop[])
		// Processing: System.Collections.Generic.IEnumerable<UniversalUI.Composition.CompositionColorGradientStop>
		// Processing: System.Collections.IEnumerable
		// Processing: System.Collections.Generic.IList<UniversalUI.Composition.CompositionColorGradientStop>
		// Skipping already implement System.Collections.Generic.IList<UniversalUI.Composition.CompositionColorGradientStop>.this[int]
		// Processing: System.Collections.Generic.ICollection<UniversalUI.Composition.CompositionColorGradientStop>
		// Skipping already implement System.Collections.Generic.ICollection<UniversalUI.Composition.CompositionColorGradientStop>.Count
		// Skipping already implement System.Collections.Generic.ICollection<UniversalUI.Composition.CompositionColorGradientStop>.IsReadOnly
	}
}
