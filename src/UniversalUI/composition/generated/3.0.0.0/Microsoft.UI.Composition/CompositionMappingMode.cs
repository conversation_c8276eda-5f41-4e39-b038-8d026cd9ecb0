// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	public enum CompositionMappingMode
	{
		// Skipping already declared field UniversalUI.Composition.CompositionMappingMode.Absolute
		// Skipping already declared field UniversalUI.Composition.CompositionMappingMode.Relative
	}
#endif
}
