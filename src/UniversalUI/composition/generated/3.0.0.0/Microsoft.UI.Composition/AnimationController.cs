// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class AnimationController : global::UniversalUI.Composition.CompositionObject
	{
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::UniversalUI.Composition.AnimationControllerProgressBehavior ProgressBehavior
		{
			get
			{
				throw new global::System.NotImplementedException("The member AnimationControllerProgressBehavior AnimationController.ProgressBehavior is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=AnimationControllerProgressBehavior%20AnimationController.ProgressBehavior");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.AnimationController", "AnimationControllerProgressBehavior AnimationController.ProgressBehavior");
			}
		}
#endif
		// Skipping already declared property Progress
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public float PlaybackRate
		{
			get
			{
				throw new global::System.NotImplementedException("The member float AnimationController.PlaybackRate is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=float%20AnimationController.PlaybackRate");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.AnimationController", "float AnimationController.PlaybackRate");
			}
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public static float MaxPlaybackRate
		{
			get
			{
				throw new global::System.NotImplementedException("The member float AnimationController.MaxPlaybackRate is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=float%20AnimationController.MaxPlaybackRate");
			}
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public static float MinPlaybackRate
		{
			get
			{
				throw new global::System.NotImplementedException("The member float AnimationController.MinPlaybackRate is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=float%20AnimationController.MinPlaybackRate");
			}
		}
#endif
		// Forced skipping of method UniversalUI.Composition.AnimationController.ProgressBehavior.get
		// Skipping already declared method UniversalUI.Composition.AnimationController.Resume()
		// Forced skipping of method UniversalUI.Composition.AnimationController.PlaybackRate.set
		// Forced skipping of method UniversalUI.Composition.AnimationController.PlaybackRate.get
		// Forced skipping of method UniversalUI.Composition.AnimationController.Progress.get
		// Forced skipping of method UniversalUI.Composition.AnimationController.Progress.set
		// Forced skipping of method UniversalUI.Composition.AnimationController.ProgressBehavior.set
		// Skipping already declared method UniversalUI.Composition.AnimationController.Pause()
		// Forced skipping of method UniversalUI.Composition.AnimationController.MaxPlaybackRate.get
		// Forced skipping of method UniversalUI.Composition.AnimationController.MinPlaybackRate.get
	}
}
