// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class PowerEasingFunction : global::UniversalUI.Composition.CompositionEasingFunction
	{
		// Skipping already declared property Mode
		// Skipping already declared property Power
		// Forced skipping of method UniversalUI.Composition.PowerEasingFunction.Mode.get
		// Forced skipping of method UniversalUI.Composition.PowerEasingFunction.Power.get
	}
}
