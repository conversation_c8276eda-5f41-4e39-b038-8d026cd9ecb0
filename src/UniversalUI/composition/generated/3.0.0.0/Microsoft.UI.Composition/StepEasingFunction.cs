// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class StepEasingFunction : global::UniversalUI.Composition.CompositionEasingFunction
	{
		// Skipping already declared property StepCount
		// Skipping already declared property IsInitialStepSingleFrame
		// Skipping already declared property IsFinalStepSingleFrame
		// Skipping already declared property InitialStep
		// Skipping already declared property FinalStep
		// Forced skipping of method UniversalUI.Composition.StepEasingFunction.StepCount.set
		// Forced skipping of method UniversalUI.Composition.StepEasingFunction.IsInitialStepSingleFrame.get
		// Forced skipping of method UniversalUI.Composition.StepEasingFunction.IsInitialStepSingleFrame.set
		// Forced skipping of method UniversalUI.Composition.StepEasingFunction.StepCount.get
		// Forced skipping of method UniversalUI.Composition.StepEasingFunction.InitialStep.get
		// Forced skipping of method UniversalUI.Composition.StepEasingFunction.FinalStep.set
		// Forced skipping of method UniversalUI.Composition.StepEasingFunction.FinalStep.get
		// Forced skipping of method UniversalUI.Composition.StepEasingFunction.InitialStep.set
		// Forced skipping of method UniversalUI.Composition.StepEasingFunction.IsFinalStepSingleFrame.get
		// Forced skipping of method UniversalUI.Composition.StepEasingFunction.IsFinalStepSingleFrame.set
	}
}
