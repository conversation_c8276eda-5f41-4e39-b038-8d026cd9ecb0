// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class Compositor : global::System.IDisposable
	{
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public float GlobalPlaybackRate
		{
			get
			{
				throw new global::System.NotImplementedException("The member float Compositor.GlobalPlaybackRate is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=float%20Compositor.GlobalPlaybackRate");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Compositor", "float Compositor.GlobalPlaybackRate");
			}
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public string Comment
		{
			get
			{
				throw new global::System.NotImplementedException("The member string Compositor.Comment is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=string%20Compositor.Comment");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Compositor", "string Compositor.Comment");
			}
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public static float MaxGlobalPlaybackRate
		{
			get
			{
				throw new global::System.NotImplementedException("The member float Compositor.MaxGlobalPlaybackRate is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=float%20Compositor.MaxGlobalPlaybackRate");
			}
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public static float MinGlobalPlaybackRate
		{
			get
			{
				throw new global::System.NotImplementedException("The member float Compositor.MinGlobalPlaybackRate is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=float%20Compositor.MinGlobalPlaybackRate");
			}
		}
#endif
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateGeometricClip()
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::UniversalUI.Composition.ColorKeyFrameAnimation CreateColorKeyFrameAnimation()
		{
			throw new global::System.NotImplementedException("The member ColorKeyFrameAnimation Compositor.CreateColorKeyFrameAnimation() is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=ColorKeyFrameAnimation%20Compositor.CreateColorKeyFrameAnimation%28%29");
		}
#endif
		// Forced skipping of method UniversalUI.Composition.Compositor.Comment.set
		// Forced skipping of method UniversalUI.Composition.Compositor.GlobalPlaybackRate.get
		// Forced skipping of method UniversalUI.Composition.Compositor.GlobalPlaybackRate.set
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::UniversalUI.Composition.CompositionContainerShape CreateContainerShape()
		{
			throw new global::System.NotImplementedException("The member CompositionContainerShape Compositor.CreateContainerShape() is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=CompositionContainerShape%20Compositor.CreateContainerShape%28%29");
		}
#endif
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateEllipseGeometry()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateLineGeometry()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreatePathGeometry()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreatePathGeometry(UniversalUI.Composition.CompositionPath)
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateRectangleGeometry()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateRoundedRectangleGeometry()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateShapeVisual()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateSpriteShape()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateSpriteShape(UniversalUI.Composition.CompositionGeometry)
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateViewBox()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateLinearEasingFunction()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateRedirectVisual()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateRedirectVisual(UniversalUI.Composition.Visual)
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::UniversalUI.Composition.BooleanKeyFrameAnimation CreateBooleanKeyFrameAnimation()
		{
			throw new global::System.NotImplementedException("The member BooleanKeyFrameAnimation Compositor.CreateBooleanKeyFrameAnimation() is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=BooleanKeyFrameAnimation%20Compositor.CreateBooleanKeyFrameAnimation%28%29");
		}
#endif
		// Forced skipping of method UniversalUI.Composition.Compositor.DispatcherQueue.get
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::UniversalUI.Composition.AnimationPropertyInfo CreateAnimationPropertyInfo()
		{
			throw new global::System.NotImplementedException("The member AnimationPropertyInfo Compositor.CreateAnimationPropertyInfo() is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=AnimationPropertyInfo%20Compositor.CreateAnimationPropertyInfo%28%29");
		}
#endif
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateRectangleClip()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateRectangleClip(float, float, float, float)
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateRectangleClip(float, float, float, float, System.Numerics.Vector2, System.Numerics.Vector2, System.Numerics.Vector2, System.Numerics.Vector2)
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateRadialGradientBrush()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateVisualSurface()
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::UniversalUI.Composition.AnimationController CreateAnimationController()
		{
			throw new global::System.NotImplementedException("The member AnimationController Compositor.CreateAnimationController() is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=AnimationController%20Compositor.CreateAnimationController%28%29");
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public void Dispose()
		{
			global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Compositor", "void Compositor.Dispose()");
		}
#endif
		// Forced skipping of method UniversalUI.Composition.Compositor.Comment.get
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateColorBrush()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateColorBrush(UniversalUI.Color)
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateContainerVisual()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateCubicBezierEasingFunction(System.Numerics.Vector2, System.Numerics.Vector2)
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateEffectFactory(Windows.Graphics.Effects.IGraphicsEffect)
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateEffectFactory(Windows.Graphics.Effects.IGraphicsEffect, System.Collections.Generic.IEnumerable<string>)
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateExpressionAnimation()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateExpressionAnimation(string)
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateInsetClip()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateInsetClip(float, float, float, float)
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateGeometricClip(UniversalUI.Composition.CompositionGeometry)
		// Skipping already declared method UniversalUI.Composition.Compositor.CreatePropertySet()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateScalarKeyFrameAnimation()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateScopedBatch(UniversalUI.Composition.CompositionBatchTypes)
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateSpriteVisual()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateSurfaceBrush()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateSurfaceBrush(UniversalUI.Composition.ICompositionSurface)
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateVector2KeyFrameAnimation()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateVector3KeyFrameAnimation()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateVector4KeyFrameAnimation()
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::UniversalUI.Composition.CompositionAnimationGroup CreateAnimationGroup()
		{
			throw new global::System.NotImplementedException("The member CompositionAnimationGroup Compositor.CreateAnimationGroup() is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=CompositionAnimationGroup%20Compositor.CreateAnimationGroup%28%29");
		}
#endif
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateBackdropBrush()
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::UniversalUI.Composition.ImplicitAnimationCollection CreateImplicitAnimationCollection()
		{
			throw new global::System.NotImplementedException("The member ImplicitAnimationCollection Compositor.CreateImplicitAnimationCollection() is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=ImplicitAnimationCollection%20Compositor.CreateImplicitAnimationCollection%28%29");
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::UniversalUI.Composition.LayerVisual CreateLayerVisual()
		{
			throw new global::System.NotImplementedException("The member LayerVisual Compositor.CreateLayerVisual() is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=LayerVisual%20Compositor.CreateLayerVisual%28%29");
		}
#endif
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateMaskBrush()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateNineGridBrush()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateStepEasingFunction()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateStepEasingFunction(int)
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateColorGradientStop()
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateColorGradientStop(float, UniversalUI.Color)
		// Skipping already declared method UniversalUI.Composition.Compositor.CreateLinearGradientBrush()
		// Skipping already declared method UniversalUI.Composition.Compositor.Compositor()
		// Forced skipping of method UniversalUI.Composition.Compositor.Compositor()
		// Forced skipping of method UniversalUI.Composition.Compositor.MaxGlobalPlaybackRate.get
		// Forced skipping of method UniversalUI.Composition.Compositor.MinGlobalPlaybackRate.get
		// Processing: System.IDisposable
	}
}
