// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class VisualCollection : global::UniversalUI.Composition.CompositionObject, global::System.Collections.Generic.IEnumerable<global::UniversalUI.Composition.Visual>
	{
		// Skipping already declared property Count
		// Forced skipping of method UniversalUI.Composition.VisualCollection.First()
		// Skipping already declared method UniversalUI.Composition.VisualCollection.InsertAbove(UniversalUI.Composition.Visual, UniversalUI.Composition.Visual)
		// Skipping already declared method UniversalUI.Composition.VisualCollection.InsertAtBottom(UniversalUI.Composition.Visual)
		// Skipping already declared method UniversalUI.Composition.VisualCollection.InsertAtTop(UniversalUI.Composition.Visual)
		// Skipping already declared method UniversalUI.Composition.VisualCollection.InsertBelow(UniversalUI.Composition.Visual, UniversalUI.Composition.Visual)
		// Skipping already declared method UniversalUI.Composition.VisualCollection.Remove(UniversalUI.Composition.Visual)
		// Skipping already declared method UniversalUI.Composition.VisualCollection.RemoveAll()
		// Forced skipping of method UniversalUI.Composition.VisualCollection.Count.get
		// Processing: System.Collections.Generic.IEnumerable<UniversalUI.Composition.Visual>
		// Processing: System.Collections.IEnumerable
	}
}
