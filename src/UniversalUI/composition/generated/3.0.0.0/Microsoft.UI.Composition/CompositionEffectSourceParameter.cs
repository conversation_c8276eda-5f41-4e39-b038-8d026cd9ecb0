// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class CompositionEffectSourceParameter
	{
		// Skipping already declared property Name
		// Forced skipping of method UniversalUI.Composition.CompositionEffectSourceParameter.Name.get
		// Skipping already declared method UniversalUI.Composition.CompositionEffectSourceParameter.CompositionEffectSourceParameter(string)
		// Forced skipping of method UniversalUI.Composition.CompositionEffectSourceParameter.CompositionEffectSourceParameter(string)
	}
}
