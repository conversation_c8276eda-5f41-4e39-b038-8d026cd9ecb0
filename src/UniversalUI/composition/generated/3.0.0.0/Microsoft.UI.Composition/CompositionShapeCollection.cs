// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class CompositionShapeCollection : global::UniversalUI.Composition.CompositionObject, global::System.Collections.Generic.IList<global::UniversalUI.Composition.CompositionShape>, global::System.Collections.Generic.IEnumerable<global::UniversalUI.Composition.CompositionShape>
	{
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public uint Size
		{
			get
			{
				throw new global::System.NotImplementedException("The member uint CompositionShapeCollection.Size is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=uint%20CompositionShapeCollection.Size");
			}
		}
#endif
		// Forced skipping of method UniversalUI.Composition.CompositionShapeCollection.IndexOf(UniversalUI.Composition.CompositionShape, out uint)
		// Forced skipping of method UniversalUI.Composition.CompositionShapeCollection.GetAt(uint)
		// Forced skipping of method UniversalUI.Composition.CompositionShapeCollection.RemoveAtEnd()
		// Forced skipping of method UniversalUI.Composition.CompositionShapeCollection.Clear()
		// Forced skipping of method UniversalUI.Composition.CompositionShapeCollection.GetMany(uint, UniversalUI.Composition.CompositionShape[])
		// Forced skipping of method UniversalUI.Composition.CompositionShapeCollection.ReplaceAll(UniversalUI.Composition.CompositionShape[])
		// Forced skipping of method UniversalUI.Composition.CompositionShapeCollection.First()
		// Forced skipping of method UniversalUI.Composition.CompositionShapeCollection.Append(UniversalUI.Composition.CompositionShape)
		// Forced skipping of method UniversalUI.Composition.CompositionShapeCollection.RemoveAt(uint)
		// Forced skipping of method UniversalUI.Composition.CompositionShapeCollection.Size.get
		// Forced skipping of method UniversalUI.Composition.CompositionShapeCollection.SetAt(uint, UniversalUI.Composition.CompositionShape)
		// Forced skipping of method UniversalUI.Composition.CompositionShapeCollection.GetView()
		// Forced skipping of method UniversalUI.Composition.CompositionShapeCollection.InsertAt(uint, UniversalUI.Composition.CompositionShape)
		// Processing: System.Collections.Generic.IList<UniversalUI.Composition.CompositionShape>
		// Skipping already implement System.Collections.Generic.IList<UniversalUI.Composition.CompositionShape>.this[int]
		// Processing: System.Collections.Generic.ICollection<UniversalUI.Composition.CompositionShape>
		// Skipping already implement System.Collections.Generic.ICollection<UniversalUI.Composition.CompositionShape>.Count
		// Skipping already implement System.Collections.Generic.ICollection<UniversalUI.Composition.CompositionShape>.IsReadOnly
		// Processing: System.Collections.Generic.IEnumerable<UniversalUI.Composition.CompositionShape>
		// Processing: System.Collections.IEnumerable
	}
}
