// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class CompositionPropertySet : global::UniversalUI.Composition.CompositionObject
	{
		// Skipping already declared method UniversalUI.Composition.CompositionPropertySet.TryGetBoolean(string, out bool)
		// Skipping already declared method UniversalUI.Composition.CompositionPropertySet.TryGetScalar(string, out float)
		// Skipping already declared method UniversalUI.Composition.CompositionPropertySet.TryGetVector3(string, out System.Numerics.Vector3)
		// Skipping already declared method UniversalUI.Composition.CompositionPropertySet.TryGetColor(string, out UniversalUI.Color)
		// Skipping already declared method UniversalUI.Composition.CompositionPropertySet.TryGetVector2(string, out System.Numerics.Vector2)
		// Skipping already declared method UniversalUI.Composition.CompositionPropertySet.TryGetVector4(string, out System.Numerics.Vector4)
		// Skipping already declared method UniversalUI.Composition.CompositionPropertySet.InsertBoolean(string, bool)
		// Skipping already declared method UniversalUI.Composition.CompositionPropertySet.InsertVector2(string, System.Numerics.Vector2)
		// Skipping already declared method UniversalUI.Composition.CompositionPropertySet.TryGetMatrix3x2(string, out System.Numerics.Matrix3x2)
		// Skipping already declared method UniversalUI.Composition.CompositionPropertySet.TryGetMatrix4x4(string, out System.Numerics.Matrix4x4)
		// Skipping already declared method UniversalUI.Composition.CompositionPropertySet.TryGetQuaternion(string, out System.Numerics.Quaternion)
		// Skipping already declared method UniversalUI.Composition.CompositionPropertySet.InsertMatrix3x2(string, System.Numerics.Matrix3x2)
		// Skipping already declared method UniversalUI.Composition.CompositionPropertySet.InsertMatrix4x4(string, System.Numerics.Matrix4x4)
		// Skipping already declared method UniversalUI.Composition.CompositionPropertySet.InsertQuaternion(string, System.Numerics.Quaternion)
		// Skipping already declared method UniversalUI.Composition.CompositionPropertySet.InsertScalar(string, float)
		// Skipping already declared method UniversalUI.Composition.CompositionPropertySet.InsertColor(string, UniversalUI.Color)
		// Skipping already declared method UniversalUI.Composition.CompositionPropertySet.InsertVector3(string, System.Numerics.Vector3)
		// Skipping already declared method UniversalUI.Composition.CompositionPropertySet.InsertVector4(string, System.Numerics.Vector4)
	}
}
