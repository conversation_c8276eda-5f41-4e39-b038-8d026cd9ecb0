// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class CompositionGradientBrush : global::UniversalUI.Composition.CompositionBrush
	{
		// Skipping already declared property TransformMatrix
		// Skipping already declared property Scale
		// Skipping already declared property RotationAngleInDegrees
		// Skipping already declared property RotationAngle
		// Skipping already declared property Offset
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::UniversalUI.Composition.CompositionColorSpace InterpolationSpace
		{
			get
			{
				throw new global::System.NotImplementedException("The member CompositionColorSpace CompositionGradientBrush.InterpolationSpace is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=CompositionColorSpace%20CompositionGradientBrush.InterpolationSpace");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.CompositionGradientBrush", "CompositionColorSpace CompositionGradientBrush.InterpolationSpace");
			}
		}
#endif
		// Skipping already declared property ExtendMode
		// Skipping already declared property CenterPoint
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::System.Numerics.Vector2 AnchorPoint
		{
			get
			{
				throw new global::System.NotImplementedException("The member Vector2 CompositionGradientBrush.AnchorPoint is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=Vector2%20CompositionGradientBrush.AnchorPoint");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.CompositionGradientBrush", "Vector2 CompositionGradientBrush.AnchorPoint");
			}
		}
#endif
		// Skipping already declared property ColorStops
		// Skipping already declared property MappingMode
		// Forced skipping of method UniversalUI.Composition.CompositionGradientBrush.RotationAngleInDegrees.get
		// Forced skipping of method UniversalUI.Composition.CompositionGradientBrush.RotationAngle.get
		// Forced skipping of method UniversalUI.Composition.CompositionGradientBrush.TransformMatrix.get
		// Forced skipping of method UniversalUI.Composition.CompositionGradientBrush.RotationAngleInDegrees.set
		// Forced skipping of method UniversalUI.Composition.CompositionGradientBrush.Scale.get
		// Forced skipping of method UniversalUI.Composition.CompositionGradientBrush.Scale.set
		// Forced skipping of method UniversalUI.Composition.CompositionGradientBrush.AnchorPoint.get
		// Forced skipping of method UniversalUI.Composition.CompositionGradientBrush.TransformMatrix.set
		// Forced skipping of method UniversalUI.Composition.CompositionGradientBrush.MappingMode.get
		// Forced skipping of method UniversalUI.Composition.CompositionGradientBrush.MappingMode.set
		// Forced skipping of method UniversalUI.Composition.CompositionGradientBrush.RotationAngle.set
		// Forced skipping of method UniversalUI.Composition.CompositionGradientBrush.AnchorPoint.set
		// Forced skipping of method UniversalUI.Composition.CompositionGradientBrush.CenterPoint.get
		// Forced skipping of method UniversalUI.Composition.CompositionGradientBrush.CenterPoint.set
		// Forced skipping of method UniversalUI.Composition.CompositionGradientBrush.ColorStops.get
		// Forced skipping of method UniversalUI.Composition.CompositionGradientBrush.ExtendMode.get
		// Forced skipping of method UniversalUI.Composition.CompositionGradientBrush.ExtendMode.set
		// Forced skipping of method UniversalUI.Composition.CompositionGradientBrush.InterpolationSpace.get
		// Forced skipping of method UniversalUI.Composition.CompositionGradientBrush.InterpolationSpace.set
		// Forced skipping of method UniversalUI.Composition.CompositionGradientBrush.Offset.get
		// Forced skipping of method UniversalUI.Composition.CompositionGradientBrush.Offset.set
	}
}
