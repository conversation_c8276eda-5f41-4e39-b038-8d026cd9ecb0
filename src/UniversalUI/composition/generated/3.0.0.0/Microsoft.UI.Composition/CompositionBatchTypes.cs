// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	public enum CompositionBatchTypes : uint
	{
		// Skipping already declared field UniversalUI.Composition.CompositionBatchTypes.None
		// Skipping already declared field UniversalUI.Composition.CompositionBatchTypes.Animation
		// Skipping already declared field UniversalUI.Composition.CompositionBatchTypes.Effect
		// Skipping already declared field UniversalUI.Composition.CompositionBatchTypes.InfiniteAnimation
		// Skipping already declared field UniversalUI.Composition.CompositionBatchTypes.AllAnimations
	}
#endif
}
