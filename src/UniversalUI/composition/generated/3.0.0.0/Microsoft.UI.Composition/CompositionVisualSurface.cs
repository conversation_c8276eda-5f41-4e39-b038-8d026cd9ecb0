// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class CompositionVisualSurface : global::UniversalUI.Composition.CompositionObject, global::UniversalUI.Composition.ICompositionSurface
	{
		// Skipping already declared property SourceVisual
		// Skipping already declared property SourceSize
		// Skipping already declared property SourceOffset
		// Forced skipping of method UniversalUI.Composition.CompositionVisualSurface.SourceSize.set
		// Forced skipping of method UniversalUI.Composition.CompositionVisualSurface.SourceVisual.get
		// Forced skipping of method UniversalUI.Composition.CompositionVisualSurface.SourceVisual.set
		// Forced skipping of method UniversalUI.Composition.CompositionVisualSurface.SourceOffset.get
		// Forced skipping of method UniversalUI.Composition.CompositionVisualSurface.SourceOffset.set
		// Forced skipping of method UniversalUI.Composition.CompositionVisualSurface.SourceSize.get
		// Processing: UniversalUI.Composition.ICompositionSurface
	}
}
