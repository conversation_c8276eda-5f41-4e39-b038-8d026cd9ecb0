// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class CompositionViewBox : global::UniversalUI.Composition.CompositionObject
	{
		// Skipping already declared property VerticalAlignmentRatio
		// Skipping already declared property Stretch
		// Skipping already declared property Size
		// Skipping already declared property Offset
		// Skipping already declared property HorizontalAlignmentRatio
		// Forced skipping of method UniversalUI.Composition.CompositionViewBox.VerticalAlignmentRatio.get
		// Forced skipping of method UniversalUI.Composition.CompositionViewBox.Stretch.set
		// Forced skipping of method UniversalUI.Composition.CompositionViewBox.HorizontalAlignmentRatio.set
		// Forced skipping of method UniversalUI.Composition.CompositionViewBox.Offset.get
		// Forced skipping of method UniversalUI.Composition.CompositionViewBox.Offset.set
		// Forced skipping of method UniversalUI.Composition.CompositionViewBox.Size.get
		// Forced skipping of method UniversalUI.Composition.CompositionViewBox.Size.set
		// Forced skipping of method UniversalUI.Composition.CompositionViewBox.Stretch.get
		// Forced skipping of method UniversalUI.Composition.CompositionViewBox.HorizontalAlignmentRatio.get
		// Forced skipping of method UniversalUI.Composition.CompositionViewBox.VerticalAlignmentRatio.set
	}
}
