// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class KeyFrameAnimation : global::UniversalUI.Composition.CompositionAnimation
	{
		// Skipping already declared property StopBehavior
		// Skipping already declared property IterationCount
		// Skipping already declared property IterationBehavior
		// Skipping already declared property Duration
		// Skipping already declared property DelayTime
		// Skipping already declared property KeyFrameCount
		// Skipping already declared property Direction
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::UniversalUI.Composition.AnimationDelayBehavior DelayBehavior
		{
			get
			{
				throw new global::System.NotImplementedException("The member AnimationDelayBehavior KeyFrameAnimation.DelayBehavior is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=AnimationDelayBehavior%20KeyFrameAnimation.DelayBehavior");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.KeyFrameAnimation", "AnimationDelayBehavior KeyFrameAnimation.DelayBehavior");
			}
		}
#endif
		// Forced skipping of method UniversalUI.Composition.KeyFrameAnimation.StopBehavior.get
		// Forced skipping of method UniversalUI.Composition.KeyFrameAnimation.StopBehavior.set
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public void InsertExpressionKeyFrame(float normalizedProgressKey, string value)
		{
			global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.KeyFrameAnimation", "void KeyFrameAnimation.InsertExpressionKeyFrame(float normalizedProgressKey, string value)");
		}
#endif
		// Forced skipping of method UniversalUI.Composition.KeyFrameAnimation.DelayBehavior.set
		// Forced skipping of method UniversalUI.Composition.KeyFrameAnimation.DelayTime.set
		// Forced skipping of method UniversalUI.Composition.KeyFrameAnimation.Duration.get
		// Forced skipping of method UniversalUI.Composition.KeyFrameAnimation.Duration.set
		// Forced skipping of method UniversalUI.Composition.KeyFrameAnimation.IterationBehavior.get
		// Forced skipping of method UniversalUI.Composition.KeyFrameAnimation.IterationBehavior.set
		// Forced skipping of method UniversalUI.Composition.KeyFrameAnimation.IterationCount.get
		// Forced skipping of method UniversalUI.Composition.KeyFrameAnimation.IterationCount.set
		// Forced skipping of method UniversalUI.Composition.KeyFrameAnimation.KeyFrameCount.get
		// Forced skipping of method UniversalUI.Composition.KeyFrameAnimation.DelayTime.get
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public void InsertExpressionKeyFrame(float normalizedProgressKey, string value, global::UniversalUI.Composition.CompositionEasingFunction easingFunction)
		{
			global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.KeyFrameAnimation", "void KeyFrameAnimation.InsertExpressionKeyFrame(float normalizedProgressKey, string value, CompositionEasingFunction easingFunction)");
		}
#endif
		// Forced skipping of method UniversalUI.Composition.KeyFrameAnimation.Direction.get
		// Forced skipping of method UniversalUI.Composition.KeyFrameAnimation.Direction.set
		// Forced skipping of method UniversalUI.Composition.KeyFrameAnimation.DelayBehavior.get
	}
}
