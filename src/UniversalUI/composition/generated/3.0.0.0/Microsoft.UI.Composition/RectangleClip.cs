// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class RectangleClip : global::UniversalUI.Composition.CompositionClip
	{
		// Skipping already declared property TopRightRadius
		// Skipping already declared property TopLeftRadius
		// Skipping already declared property Top
		// Skipping already declared property Right
		// Skipping already declared property Left
		// Skipping already declared property BottomRightRadius
		// Skipping already declared property BottomLeftRadius
		// Skipping already declared property Bottom
		// Forced skipping of method UniversalUI.Composition.RectangleClip.Bottom.get
		// Forced skipping of method UniversalUI.Composition.RectangleClip.Top.get
		// Forced skipping of method UniversalUI.Composition.RectangleClip.Top.set
		// Forced skipping of method UniversalUI.Composition.RectangleClip.TopLeftRadius.set
		// Forced skipping of method UniversalUI.Composition.RectangleClip.Right.set
		// Forced skipping of method UniversalUI.Composition.RectangleClip.Bottom.set
		// Forced skipping of method UniversalUI.Composition.RectangleClip.BottomLeftRadius.get
		// Forced skipping of method UniversalUI.Composition.RectangleClip.BottomLeftRadius.set
		// Forced skipping of method UniversalUI.Composition.RectangleClip.BottomRightRadius.get
		// Forced skipping of method UniversalUI.Composition.RectangleClip.BottomRightRadius.set
		// Forced skipping of method UniversalUI.Composition.RectangleClip.Left.get
		// Forced skipping of method UniversalUI.Composition.RectangleClip.Left.set
		// Forced skipping of method UniversalUI.Composition.RectangleClip.Right.get
		// Forced skipping of method UniversalUI.Composition.RectangleClip.TopLeftRadius.get
		// Forced skipping of method UniversalUI.Composition.RectangleClip.TopRightRadius.get
		// Forced skipping of method UniversalUI.Composition.RectangleClip.TopRightRadius.set
	}
}
