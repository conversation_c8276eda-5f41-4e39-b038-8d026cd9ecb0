// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class CompositionSurfaceBrush : global::UniversalUI.Composition.CompositionBrush
	{
		// Skipping already declared property VerticalAlignmentRatio
		// Skipping already declared property Surface
		// Skipping already declared property Stretch
		// Skipping already declared property HorizontalAlignmentRatio
		// Skipping already declared property BitmapInterpolationMode
		// Skipping already declared property TransformMatrix
		// Skipping already declared property Scale
		// Skipping already declared property RotationAngleInDegrees
		// Skipping already declared property RotationAngle
		// Skipping already declared property Offset
		// Skipping already declared property CenterPoint
		// Skipping already declared property AnchorPoint
		// Skipping already declared property SnapToPixels
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.RotationAngle.set
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.Scale.get
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.BitmapInterpolationMode.set
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.HorizontalAlignmentRatio.get
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.HorizontalAlignmentRatio.set
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.Stretch.get
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.BitmapInterpolationMode.get
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.Surface.get
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.Surface.set
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.VerticalAlignmentRatio.get
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.VerticalAlignmentRatio.set
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.AnchorPoint.get
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.AnchorPoint.set
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.CenterPoint.get
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.CenterPoint.set
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.Offset.get
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.Offset.set
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.RotationAngle.get
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.Stretch.set
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.RotationAngleInDegrees.get
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.RotationAngleInDegrees.set
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.Scale.set
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.TransformMatrix.get
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.TransformMatrix.set
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.SnapToPixels.get
		// Forced skipping of method UniversalUI.Composition.CompositionSurfaceBrush.SnapToPixels.set
	}
}
