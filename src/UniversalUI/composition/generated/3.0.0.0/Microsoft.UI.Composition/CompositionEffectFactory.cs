// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class CompositionEffectFactory : global::UniversalUI.Composition.CompositionObject
	{
		// Skipping already declared property ExtendedError
		// Skipping already declared property LoadStatus
		// Skipping already declared method UniversalUI.Composition.CompositionEffectFactory.CreateBrush()
		// Forced skipping of method UniversalUI.Composition.CompositionEffectFactory.LoadStatus.get
		// Forced skipping of method UniversalUI.Composition.CompositionEffectFactory.ExtendedError.get
	}
}
