// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class CompositionNineGridBrush : global::UniversalUI.Composition.CompositionBrush
	{
		// Skipping already declared property TopInsetScale
		// Skipping already declared property TopInset
		// Skipping already declared property Source
		// Skipping already declared property RightInsetScale
		// Skipping already declared property RightInset
		// Skipping already declared property LeftInsetScale
		// Skipping already declared property LeftInset
		// Skipping already declared property IsCenterHollow
		// Skipping already declared property BottomInsetScale
		// Skipping already declared property BottomInset
		// Skipping already declared method UniversalUI.Composition.CompositionNineGridBrush.SetInsets(float, float, float, float)
		// Forced skipping of method UniversalUI.Composition.CompositionNineGridBrush.IsCenterHollow.get
		// Forced skipping of method UniversalUI.Composition.CompositionNineGridBrush.Source.set
		// Forced skipping of method UniversalUI.Composition.CompositionNineGridBrush.TopInset.set
		// Forced skipping of method UniversalUI.Composition.CompositionNineGridBrush.TopInsetScale.get
		// Forced skipping of method UniversalUI.Composition.CompositionNineGridBrush.TopInsetScale.set
		// Skipping already declared method UniversalUI.Composition.CompositionNineGridBrush.SetInsets(float)
		// Forced skipping of method UniversalUI.Composition.CompositionNineGridBrush.LeftInsetScale.get
		// Skipping already declared method UniversalUI.Composition.CompositionNineGridBrush.SetInsetScales(float)
		// Skipping already declared method UniversalUI.Composition.CompositionNineGridBrush.SetInsetScales(float, float, float, float)
		// Forced skipping of method UniversalUI.Composition.CompositionNineGridBrush.BottomInset.set
		// Forced skipping of method UniversalUI.Composition.CompositionNineGridBrush.BottomInsetScale.get
		// Forced skipping of method UniversalUI.Composition.CompositionNineGridBrush.BottomInsetScale.set
		// Forced skipping of method UniversalUI.Composition.CompositionNineGridBrush.TopInset.get
		// Forced skipping of method UniversalUI.Composition.CompositionNineGridBrush.IsCenterHollow.set
		// Forced skipping of method UniversalUI.Composition.CompositionNineGridBrush.LeftInset.get
		// Forced skipping of method UniversalUI.Composition.CompositionNineGridBrush.LeftInset.set
		// Forced skipping of method UniversalUI.Composition.CompositionNineGridBrush.BottomInset.get
		// Forced skipping of method UniversalUI.Composition.CompositionNineGridBrush.LeftInsetScale.set
		// Forced skipping of method UniversalUI.Composition.CompositionNineGridBrush.RightInset.get
		// Forced skipping of method UniversalUI.Composition.CompositionNineGridBrush.RightInset.set
		// Forced skipping of method UniversalUI.Composition.CompositionNineGridBrush.RightInsetScale.get
		// Forced skipping of method UniversalUI.Composition.CompositionNineGridBrush.RightInsetScale.set
		// Forced skipping of method UniversalUI.Composition.CompositionNineGridBrush.Source.get
	}
}
