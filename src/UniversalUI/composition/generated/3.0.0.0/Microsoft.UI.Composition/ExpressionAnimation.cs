// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class ExpressionAnimation : global::UniversalUI.Composition.CompositionAnimation
	{
		// Skipping already declared property Expression
		// Forced skipping of method UniversalUI.Composition.ExpressionAnimation.Expression.set
		// Forced skipping of method UniversalUI.Composition.ExpressionAnimation.Expression.get
	}
}
