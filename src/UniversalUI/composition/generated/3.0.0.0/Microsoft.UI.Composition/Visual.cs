// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class Visual : global::UniversalUI.Composition.CompositionObject
	{
		// Skipping already declared property TransformMatrix
		// Skipping already declared property Size
		// Skipping already declared property Scale
		// Skipping already declared property RotationAxis
		// Skipping already declared property RotationAngleInDegrees
		// Skipping already declared property RotationAngle
		// Skipping already declared property Orientation
		// Skipping already declared property Opacity
		// Skipping already declared property Offset
		// Skipping already declared property IsVisible
		// Skipping already declared property CompositeMode
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || false || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__NETSTD_REFERENCE__")]
		public global::UniversalUI.Composition.CompositionClip Clip
		{
			get
			{
				throw new global::System.NotImplementedException("The member CompositionClip Visual.Clip is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=CompositionClip%20Visual.Clip");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Visual", "CompositionClip Visual.Clip");
			}
		}
#endif
		// Skipping already declared property CenterPoint
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::UniversalUI.Composition.CompositionBorderMode BorderMode
		{
			get
			{
				throw new global::System.NotImplementedException("The member CompositionBorderMode Visual.BorderMode is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=CompositionBorderMode%20Visual.BorderMode");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Visual", "CompositionBorderMode Visual.BorderMode");
			}
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::UniversalUI.Composition.CompositionBackfaceVisibility BackfaceVisibility
		{
			get
			{
				throw new global::System.NotImplementedException("The member CompositionBackfaceVisibility Visual.BackfaceVisibility is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=CompositionBackfaceVisibility%20Visual.BackfaceVisibility");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Visual", "CompositionBackfaceVisibility Visual.BackfaceVisibility");
			}
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || false || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__NETSTD_REFERENCE__")]
		public global::System.Numerics.Vector2 AnchorPoint
		{
			get
			{
				throw new global::System.NotImplementedException("The member Vector2 Visual.AnchorPoint is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=Vector2%20Visual.AnchorPoint");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Visual", "Vector2 Visual.AnchorPoint");
			}
		}
#endif
		// Skipping already declared property Parent
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::System.Numerics.Vector2 RelativeSizeAdjustment
		{
			get
			{
				throw new global::System.NotImplementedException("The member Vector2 Visual.RelativeSizeAdjustment is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=Vector2%20Visual.RelativeSizeAdjustment");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Visual", "Vector2 Visual.RelativeSizeAdjustment");
			}
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::System.Numerics.Vector3 RelativeOffsetAdjustment
		{
			get
			{
				throw new global::System.NotImplementedException("The member Vector3 Visual.RelativeOffsetAdjustment is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=Vector3%20Visual.RelativeOffsetAdjustment");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Visual", "Vector3 Visual.RelativeOffsetAdjustment");
			}
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::UniversalUI.Composition.Visual ParentForTransform
		{
			get
			{
				throw new global::System.NotImplementedException("The member Visual Visual.ParentForTransform is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=Visual%20Visual.ParentForTransform");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Visual", "Visual Visual.ParentForTransform");
			}
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public bool IsHitTestVisible
		{
			get
			{
				throw new global::System.NotImplementedException("The member bool Visual.IsHitTestVisible is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=bool%20Visual.IsHitTestVisible");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Visual", "bool Visual.IsHitTestVisible");
			}
		}
#endif
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public bool IsPixelSnappingEnabled
		{
			get
			{
				throw new global::System.NotImplementedException("The member bool Visual.IsPixelSnappingEnabled is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=bool%20Visual.IsPixelSnappingEnabled");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.Visual", "bool Visual.IsPixelSnappingEnabled");
			}
		}
#endif
		// Forced skipping of method UniversalUI.Composition.Visual.IsPixelSnappingEnabled.set
		// Forced skipping of method UniversalUI.Composition.Visual.RelativeSizeAdjustment.get
		// Forced skipping of method UniversalUI.Composition.Visual.IsHitTestVisible.get
		// Forced skipping of method UniversalUI.Composition.Visual.IsHitTestVisible.set
		// Forced skipping of method UniversalUI.Composition.Visual.IsPixelSnappingEnabled.get
		// Forced skipping of method UniversalUI.Composition.Visual.IsVisible.set
		// Forced skipping of method UniversalUI.Composition.Visual.RelativeOffsetAdjustment.set
		// Forced skipping of method UniversalUI.Composition.Visual.RelativeSizeAdjustment.set
		// Forced skipping of method UniversalUI.Composition.Visual.BackfaceVisibility.set
		// Forced skipping of method UniversalUI.Composition.Visual.AnchorPoint.set
		// Forced skipping of method UniversalUI.Composition.Visual.BackfaceVisibility.get
		// Forced skipping of method UniversalUI.Composition.Visual.AnchorPoint.get
		// Forced skipping of method UniversalUI.Composition.Visual.BorderMode.get
		// Forced skipping of method UniversalUI.Composition.Visual.BorderMode.set
		// Forced skipping of method UniversalUI.Composition.Visual.CenterPoint.get
		// Forced skipping of method UniversalUI.Composition.Visual.CenterPoint.set
		// Forced skipping of method UniversalUI.Composition.Visual.Clip.get
		// Forced skipping of method UniversalUI.Composition.Visual.Clip.set
		// Forced skipping of method UniversalUI.Composition.Visual.CompositeMode.get
		// Forced skipping of method UniversalUI.Composition.Visual.CompositeMode.set
		// Forced skipping of method UniversalUI.Composition.Visual.IsVisible.get
		// Forced skipping of method UniversalUI.Composition.Visual.Offset.get
		// Forced skipping of method UniversalUI.Composition.Visual.Offset.set
		// Forced skipping of method UniversalUI.Composition.Visual.Opacity.get
		// Forced skipping of method UniversalUI.Composition.Visual.Opacity.set
		// Forced skipping of method UniversalUI.Composition.Visual.Orientation.get
		// Forced skipping of method UniversalUI.Composition.Visual.Orientation.set
		// Forced skipping of method UniversalUI.Composition.Visual.Parent.get
		// Forced skipping of method UniversalUI.Composition.Visual.RotationAngle.get
		// Forced skipping of method UniversalUI.Composition.Visual.RotationAngle.set
		// Forced skipping of method UniversalUI.Composition.Visual.RotationAngleInDegrees.get
		// Forced skipping of method UniversalUI.Composition.Visual.RotationAngleInDegrees.set
		// Forced skipping of method UniversalUI.Composition.Visual.RotationAxis.get
		// Forced skipping of method UniversalUI.Composition.Visual.RotationAxis.set
		// Forced skipping of method UniversalUI.Composition.Visual.Scale.get
		// Forced skipping of method UniversalUI.Composition.Visual.Scale.set
		// Forced skipping of method UniversalUI.Composition.Visual.Size.get
		// Forced skipping of method UniversalUI.Composition.Visual.Size.set
		// Forced skipping of method UniversalUI.Composition.Visual.TransformMatrix.get
		// Forced skipping of method UniversalUI.Composition.Visual.TransformMatrix.set
		// Forced skipping of method UniversalUI.Composition.Visual.ParentForTransform.get
		// Forced skipping of method UniversalUI.Composition.Visual.ParentForTransform.set
		// Forced skipping of method UniversalUI.Composition.Visual.RelativeOffsetAdjustment.get
	}
}
