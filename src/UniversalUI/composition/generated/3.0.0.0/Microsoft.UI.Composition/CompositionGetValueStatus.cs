// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	public enum CompositionGetValueStatus
	{
		// Skipping already declared field UniversalUI.Composition.CompositionGetValueStatus.Succeeded
		// Skipping already declared field UniversalUI.Composition.CompositionGetValueStatus.TypeMismatch
		// Skipping already declared field UniversalUI.Composition.CompositionGetValueStatus.NotFound
	}
#endif
}
