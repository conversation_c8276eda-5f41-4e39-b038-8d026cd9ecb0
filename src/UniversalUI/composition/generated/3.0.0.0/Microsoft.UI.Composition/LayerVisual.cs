// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class LayerVisual : global::UniversalUI.Composition.ContainerVisual
	{
#if __ANDROID__ || __IOS__ || __TVOS__ || IS_UNIT_TESTS || __WASM__ || __SKIA__ || __NETSTD_REFERENCE__
		[global::UniversalUI.NotImplemented("__ANDROID__", "__IOS__", "__TVOS__", "IS_UNIT_TESTS", "__WASM__", "__SKIA__", "__NETSTD_REFERENCE__")]
		public global::UniversalUI.Composition.CompositionEffectBrush Effect
		{
			get
			{
				throw new global::System.NotImplementedException("The member CompositionEffectBrush LayerVisual.Effect is not implemented. For more information, visit https://aka.platform.uno/notimplemented#m=CompositionEffectBrush%20LayerVisual.Effect");
			}
			set
			{
				global::Windows.Foundation.Metadata.ApiInformation.TryRaiseNotImplemented("UniversalUI.Composition.LayerVisual", "CompositionEffectBrush LayerVisual.Effect");
			}
		}
#endif
		// Forced skipping of method UniversalUI.Composition.LayerVisual.Effect.get
		// Forced skipping of method UniversalUI.Composition.LayerVisual.Effect.set
		// Forced skipping of method UniversalUI.Composition.LayerVisual.Shadow.set
		// Forced skipping of method UniversalUI.Composition.LayerVisual.Shadow.get
	}
}
