// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class CompositionColorGradientStop : global::UniversalUI.Composition.CompositionObject
	{
		// Skipping already declared property Offset
		// Skipping already declared property Color
		// Forced skipping of method UniversalUI.Composition.CompositionColorGradientStop.Color.set
		// Forced skipping of method UniversalUI.Composition.CompositionColorGradientStop.Offset.set
		// Forced skipping of method UniversalUI.Composition.CompositionColorGradientStop.Color.get
		// Forced skipping of method UniversalUI.Composition.CompositionColorGradientStop.Offset.get
	}
}
