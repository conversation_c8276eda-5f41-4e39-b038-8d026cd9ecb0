// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class CompositionLinearGradientBrush : global::UniversalUI.Composition.CompositionGradientBrush
	{
		// Skipping already declared property StartPoint
		// Skipping already declared property EndPoint
		// Forced skipping of method UniversalUI.Composition.CompositionLinearGradientBrush.StartPoint.get
		// Forced skipping of method UniversalUI.Composition.CompositionLinearGradientBrush.EndPoint.get
		// Forced skipping of method UniversalUI.Composition.CompositionLinearGradientBrush.EndPoint.set
		// Forced skipping of method UniversalUI.Composition.CompositionLinearGradientBrush.StartPoint.set
	}
}
