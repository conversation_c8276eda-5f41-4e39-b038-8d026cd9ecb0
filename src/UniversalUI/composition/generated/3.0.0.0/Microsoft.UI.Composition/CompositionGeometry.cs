// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class CompositionGeometry : global::UniversalUI.Composition.CompositionObject
	{
		// Skipping already declared property TrimStart
		// Skipping already declared property TrimOffset
		// Skipping already declared property TrimEnd
		// Forced skipping of method UniversalUI.Composition.CompositionGeometry.TrimOffset.get
		// Forced skipping of method UniversalUI.Composition.CompositionGeometry.TrimEnd.set
		// Forced skipping of method UniversalUI.Composition.CompositionGeometry.TrimStart.set
		// Forced skipping of method UniversalUI.Composition.CompositionGeometry.TrimOffset.set
		// Forced skipping of method UniversalUI.Composition.CompositionGeometry.TrimEnd.get
		// Forced skipping of method UniversalUI.Composition.CompositionGeometry.TrimStart.get
	}
}
