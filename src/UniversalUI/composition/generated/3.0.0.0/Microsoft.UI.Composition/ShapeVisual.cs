// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class ShapeVisual : global::UniversalUI.Composition.ContainerVisual
	{
		// Skipping already declared property ViewBox
		// Skipping already declared property Shapes
		// Forced skipping of method UniversalUI.Composition.ShapeVisual.ViewBox.set
		// Forced skipping of method UniversalUI.Composition.ShapeVisual.ViewBox.get
		// Forced skipping of method UniversalUI.Composition.ShapeVisual.Shapes.get
	}
}
