// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class CompositionMaskBrush : global::UniversalUI.Composition.CompositionBrush
	{
		// Skipping already declared property Source
		// Skipping already declared property Mask
		// Forced skipping of method UniversalUI.Composition.CompositionMaskBrush.Source.get
		// Forced skipping of method UniversalUI.Composition.CompositionMaskBrush.Source.set
		// Forced skipping of method UniversalUI.Composition.CompositionMaskBrush.Mask.set
		// Forced skipping of method UniversalUI.Composition.CompositionMaskBrush.Mask.get
	}
}
