// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class CompositionGeometricClip : global::UniversalUI.Composition.CompositionClip
	{
		// Skipping already declared property ViewBox
		// Skipping already declared property Geometry
		// Forced skipping of method UniversalUI.Composition.CompositionGeometricClip.Geometry.set
		// Forced skipping of method UniversalUI.Composition.CompositionGeometricClip.ViewBox.get
		// Forced skipping of method UniversalUI.Composition.CompositionGeometricClip.ViewBox.set
		// Forced skipping of method UniversalUI.Composition.CompositionGeometricClip.Geometry.get
	}
}
