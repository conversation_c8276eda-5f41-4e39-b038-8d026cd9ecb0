// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class RedirectVisual : global::UniversalUI.Composition.ContainerVisual
	{
		// Skipping already declared property Source
		// Forced skipping of method UniversalUI.Composition.RedirectVisual.Source.get
		// Forced skipping of method UniversalUI.Composition.RedirectVisual.Source.set
	}
}
