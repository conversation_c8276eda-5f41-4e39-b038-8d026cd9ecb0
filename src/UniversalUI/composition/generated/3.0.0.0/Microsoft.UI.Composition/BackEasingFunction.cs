// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class BackEasingFunction : global::UniversalUI.Composition.CompositionEasingFunction
	{
		// Skipping already declared property Amplitude
		// Skipping already declared property Mode
		// Forced skipping of method UniversalUI.Composition.BackEasingFunction.Amplitude.get
		// Forced skipping of method UniversalUI.Composition.BackEasingFunction.Mode.get
	}
}
