// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class CompositionEasingFunction : global::UniversalUI.Composition.CompositionObject
	{
		// Skipping already declared method UniversalUI.Composition.CompositionEasingFunction.CreateCubicBezierEasingFunction(UniversalUI.Composition.Compositor, System.Numerics.Vector2, System.Numerics.Vector2)
		// Skipping already declared method UniversalUI.Composition.CompositionEasingFunction.CreateLinearEasingFunction(UniversalUI.Composition.Compositor)
		// Skipping already declared method UniversalUI.Composition.CompositionEasingFunction.CreateStepEasingFunction(UniversalUI.Composition.Compositor)
		// Skipping already declared method UniversalUI.Composition.CompositionEasingFunction.CreateStepEasingFunction(UniversalUI.Composition.Compositor, int)
		// Skipping already declared method UniversalUI.Composition.CompositionEasingFunction.CreateBackEasingFunction(UniversalUI.Composition.Compositor, UniversalUI.Composition.CompositionEasingFunctionMode, float)
		// Skipping already declared method UniversalUI.Composition.CompositionEasingFunction.CreateBounceEasingFunction(UniversalUI.Composition.Compositor, UniversalUI.Composition.CompositionEasingFunctionMode, int, float)
		// Skipping already declared method UniversalUI.Composition.CompositionEasingFunction.CreateCircleEasingFunction(UniversalUI.Composition.Compositor, UniversalUI.Composition.CompositionEasingFunctionMode)
		// Skipping already declared method UniversalUI.Composition.CompositionEasingFunction.CreateElasticEasingFunction(UniversalUI.Composition.Compositor, UniversalUI.Composition.CompositionEasingFunctionMode, int, float)
		// Skipping already declared method UniversalUI.Composition.CompositionEasingFunction.CreateExponentialEasingFunction(UniversalUI.Composition.Compositor, UniversalUI.Composition.CompositionEasingFunctionMode, float)
		// Skipping already declared method UniversalUI.Composition.CompositionEasingFunction.CreatePowerEasingFunction(UniversalUI.Composition.Compositor, UniversalUI.Composition.CompositionEasingFunctionMode, float)
		// Skipping already declared method UniversalUI.Composition.CompositionEasingFunction.CreateSineEasingFunction(UniversalUI.Composition.Compositor, UniversalUI.Composition.CompositionEasingFunctionMode)
	}
}
