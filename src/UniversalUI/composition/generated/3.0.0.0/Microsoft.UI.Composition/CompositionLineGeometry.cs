// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class CompositionLineGeometry : global::UniversalUI.Composition.CompositionGeometry
	{
		// Skipping already declared property Start
		// Skipping already declared property End
		// Forced skipping of method UniversalUI.Composition.CompositionLineGeometry.Start.set
		// Forced skipping of method UniversalUI.Composition.CompositionLineGeometry.Start.get
		// Forced skipping of method UniversalUI.Composition.CompositionLineGeometry.End.set
		// Forced skipping of method UniversalUI.Composition.CompositionLineGeometry.End.get
	}
}
