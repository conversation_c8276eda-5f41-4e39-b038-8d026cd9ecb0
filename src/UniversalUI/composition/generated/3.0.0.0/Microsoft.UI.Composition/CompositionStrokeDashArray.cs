// This file is copied, with modifications, from the Uno project

#nullable disable

// <auto-generated>
#pragma warning disable 108 // new keyword hiding
#pragma warning disable 114 // new keyword hiding
namespace UniversalUI.Composition
{
#if false || false || false || false || false || false || false
	[global::UniversalUI.NotImplemented]
#endif
	public partial class CompositionStrokeDashArray : global::UniversalUI.Composition.CompositionObject, global::System.Collections.Generic.IList<float>, global::System.Collections.Generic.IEnumerable<float>
	{
		// Skipping already declared property Size
		// Forced skipping of method UniversalUI.Composition.CompositionStrokeDashArray.Clear()
		// Forced skipping of method UniversalUI.Composition.CompositionStrokeDashArray.RemoveAt(uint)
		// Forced skipping of method UniversalUI.Composition.CompositionStrokeDashArray.Append(float)
		// Forced skipping of method UniversalUI.Composition.CompositionStrokeDashArray.RemoveAtEnd()
		// Forced skipping of method UniversalUI.Composition.CompositionStrokeDashArray.GetAt(uint)
		// Forced skipping of method UniversalUI.Composition.CompositionStrokeDashArray.GetMany(uint, float[])
		// Forced skipping of method UniversalUI.Composition.CompositionStrokeDashArray.ReplaceAll(float[])
		// Forced skipping of method UniversalUI.Composition.CompositionStrokeDashArray.First()
		// Forced skipping of method UniversalUI.Composition.CompositionStrokeDashArray.Size.get
		// Forced skipping of method UniversalUI.Composition.CompositionStrokeDashArray.GetView()
		// Forced skipping of method UniversalUI.Composition.CompositionStrokeDashArray.IndexOf(float, out uint)
		// Forced skipping of method UniversalUI.Composition.CompositionStrokeDashArray.SetAt(uint, float)
		// Forced skipping of method UniversalUI.Composition.CompositionStrokeDashArray.InsertAt(uint, float)
		// Processing: System.Collections.Generic.IList<float>
		// Skipping already implement System.Collections.Generic.IList<float>.this[int]
		// Processing: System.Collections.Generic.ICollection<float>
		// Skipping already implement System.Collections.Generic.ICollection<float>.Count
		// Skipping already implement System.Collections.Generic.ICollection<float>.IsReadOnly
		// Processing: System.Collections.Generic.IEnumerable<float>
		// Processing: System.Collections.IEnumerable
	}
}
